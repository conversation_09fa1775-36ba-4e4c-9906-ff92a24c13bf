services:
  # Redis cache
  redis:
    image: redis:7-alpine
    volumes:
      - redis_data:/data
    networks:
      - pilarj-proxy_proxyNet
    deploy:
      replicas: 1

  # PHP/Symfony backend
  backend:
    image: ${CURRENT_BACKEND_IMAGE:-pujcovna-backend:latest}
    extra_hosts:
      - "host.docker.internal:host-gateway"
    volumes:
      - ./backend:/var/www/html
      - ./backend/var:/var/www/html/var
      - ./backend/vendor:/var/www/html/vendor
      - ./.env:/var/www/html/.env
    depends_on:
      - redis
    networks:
      - pilarj-proxy_proxyNet
    deploy:
      replicas: 2
      update_config:
        parallelism: 1
        delay: 30s
        failure_action: rollback
        order: start-first
      restart_policy:
        condition: on-failure
        delay: 5s
        max_attempts: 3
    labels:
      - "traefik.enable=true"
      - "traefik.http.routers.backend.rule=Host(`pujcovna.pilarj.cz`) && PathPrefix(`/api/`)"
      - "traefik.http.routers.backend.entrypoints=websecure"
      - "traefik.http.routers.backend.tls.certresolver=letsencrypt"
      - "traefik.http.services.backend.loadbalancer.server.port=8000"

  # Next.js frontend
  frontend:
    image: ${CURRENT_FRONTEND_IMAGE:-pujcovna-frontend:latest}
    volumes:
      - ./frontend:/app
      - /app/node_modules
      - /app/.next
      - ./.env:/app/.env
    depends_on:
      - backend
    networks:
      - pilarj-proxy_proxyNet
    deploy:
      replicas: 2
      update_config:
        parallelism: 1
        delay: 30s
        failure_action: rollback
        order: start-first
      restart_policy:
        condition: on-failure
        delay: 5s
        max_attempts: 3
    labels:
      - "traefik.enable=true"
      - "traefik.http.routers.frontend.rule=Host(`pujcovna.pilarj.cz`) && !PathPrefix(`/api/`)"
      - "traefik.http.routers.frontend.entrypoints=websecure"
      - "traefik.http.routers.frontend.tls.certresolver=letsencrypt"
      - "traefik.http.services.frontend.loadbalancer.server.port=3000"

  # Traefik reverse proxy
  traefik:
    image: traefik:v2.10
    ports:
      - "18080:80"
      - "18443:443"
      - "18090:8080"  # Dashboard
    volumes:
      - /var/run/docker.sock:/var/run/docker.sock:ro
      - ./docker/traefik/traefik.server.yml:/etc/traefik/traefik.yml:ro
      - traefik_letsencrypt:/letsencrypt
    networks:
      - pilarj-proxy_proxyNet
    deploy:
      replicas: 1
    labels:
      - "traefik.enable=true"

volumes:
  redis_data:
  traefik_letsencrypt:

networks:
  pilarj-proxy_proxyNet:
    external: true
