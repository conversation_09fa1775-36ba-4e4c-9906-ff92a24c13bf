# Pujčovna - Root .htaccess
# Přesměrování všech requestů do public/ složky

# Zapnutí URL rewriting
RewriteEngine On

# Přesměrování všech requestů do public/ složky
# Pokud soubor nebo složka neexistuje v rootu, přesměruj do public/
RewriteCond %{REQUEST_FILENAME} !-f
RewriteCond %{REQUEST_FILENAME} !-d
RewriteRule ^(.*)$ public/$1 [L]

# Pokud soubor existuje v rootu, ale také v public/, preferuj public/
RewriteCond %{REQUEST_FILENAME} -f
RewriteCond %{DOCUMENT_ROOT}/public/%{REQUEST_URI} -f
RewriteRule ^(.*)$ public/$1 [L]

# Bezpečnost - zakázání přístupu k citlivým souborům
<Files ".env*">
    Require all denied
</Files>

<Files "composer.*">
    Require all denied
</Files>

<Files "*.yml">
    Require all denied
</Files>

<Files "*.yaml">
    Require all denied
</Files>

# Zakázání přístupu k vendor/ a config/ složkám
RewriteRule ^(vendor|config|var|src|bin|migrations)/ - [F,L]

# Zakázání přístupu k backup souborům
<FilesMatch "\.(bak|backup|old|tmp)$">
    Require all denied
</FilesMatch>

# Optimalizace - komprese
<IfModule mod_deflate.c>
    AddOutputFilterByType DEFLATE text/plain
    AddOutputFilterByType DEFLATE text/html
    AddOutputFilterByType DEFLATE text/xml
    AddOutputFilterByType DEFLATE text/css
    AddOutputFilterByType DEFLATE application/xml
    AddOutputFilterByType DEFLATE application/xhtml+xml
    AddOutputFilterByType DEFLATE application/rss+xml
    AddOutputFilterByType DEFLATE application/javascript
    AddOutputFilterByType DEFLATE application/x-javascript
    AddOutputFilterByType DEFLATE application/json
</IfModule>

# Cache hlavičky pro statické soubory
<IfModule mod_expires.c>
    ExpiresActive On
    ExpiresByType image/jpg "access plus 1 month"
    ExpiresByType image/jpeg "access plus 1 month"
    ExpiresByType image/gif "access plus 1 month"
    ExpiresByType image/png "access plus 1 month"
    ExpiresByType text/css "access plus 1 month"
    ExpiresByType application/pdf "access plus 1 month"
    ExpiresByType application/javascript "access plus 1 month"
    ExpiresByType application/x-javascript "access plus 1 month"
    ExpiresByType application/x-shockwave-flash "access plus 1 month"
    ExpiresByType image/x-icon "access plus 1 year"
    ExpiresDefault "access plus 2 days"
</IfModule>

# Bezpečnostní hlavičky
<IfModule mod_headers.c>
    Header always set X-Content-Type-Options nosniff
    Header always set X-Frame-Options DENY
    Header always set X-XSS-Protection "1; mode=block"
    Header always set Referrer-Policy "strict-origin-when-cross-origin"
    Header always set Permissions-Policy "geolocation=(), microphone=(), camera=()"
</IfModule>
