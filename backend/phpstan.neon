includes:
    - vendor/phpstan/phpstan-symfony/extension.neon
    - vendor/phpstan/phpstan-doctrine/extension.neon

parameters:
    level: 5
    paths:
        - src
    excludePaths:
        - src/Kernel.php
    tmpDir: var/cache/phpstan
    treatPhpDocTypesAsCertain: false
    symfony:
        container_xml_path: var/cache/dev/App_KernelDevDebugContainer.xml
    ignoreErrors:
        # Ignore type casting issues in controllers
        - '#Parameter .* expects .*, .* given\.#'
