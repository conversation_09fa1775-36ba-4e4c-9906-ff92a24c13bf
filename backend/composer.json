{"name": "pujcovna/backend", "description": "Pujčovna P2P Backend API", "type": "project", "license": "proprietary", "minimum-stability": "stable", "prefer-stable": true, "require": {"php": ">=8.2", "ext-ctype": "*", "ext-iconv": "*", "ext-pdo": "*", "ext-pdo_pgsql": "*", "api-platform/core": "^3.1", "doctrine/doctrine-bundle": "^2.10", "doctrine/doctrine-migrations-bundle": "^3.2", "doctrine/orm": "^2.16", "lexik/jwt-authentication-bundle": "^2.19", "nelmio/cors-bundle": "^2.3", "sentry/sentry-symfony": "^5.2", "symfony/console": "6.4.*", "symfony/dotenv": "6.4.*", "symfony/expression-language": "6.4.*", "symfony/flex": "^2", "symfony/framework-bundle": "6.4.*", "symfony/mailer": "6.4.*", "symfony/monolog-bundle": "^3.0", "symfony/runtime": "6.4.*", "symfony/security-bundle": "6.4.*", "symfony/serializer": "6.4.*", "symfony/twig-bundle": "6.4.*", "symfony/validator": "6.4.*", "symfony/yaml": "6.4.*", "doctrine/doctrine-fixtures-bundle": "^3.4"}, "require-dev": {"friendsofphp/php-cs-fixer": "^3.0", "phpstan/phpstan": "^1.10", "phpstan/phpstan-doctrine": "^1.3", "phpstan/phpstan-symfony": "^1.3", "sebastian/diff": "^6.0"}, "suggest": {"ext-redis": "For Redis cache support"}, "config": {"allow-plugins": {"php-http/discovery": true, "symfony/flex": true, "symfony/runtime": true}, "sort-packages": true}, "autoload": {"psr-4": {"App\\": "src/"}}, "autoload-dev": {"psr-4": {"App\\Tests\\": "tests/"}}, "replace": {"symfony/polyfill-ctype": "*", "symfony/polyfill-iconv": "*", "symfony/polyfill-php72": "*", "symfony/polyfill-php73": "*", "symfony/polyfill-php74": "*", "symfony/polyfill-php80": "*", "symfony/polyfill-php81": "*"}, "scripts": {"auto-scripts": {"cache:clear": "symfony-cmd", "cache:warmup": "symfony-cmd"}, "post-install-cmd": ["@auto-scripts"], "post-update-cmd": ["@auto-scripts"], "cs-fix": "php-cs-fixer fix", "cs-check": "php-cs-fixer fix --dry-run --diff", "phpstan": "php -d memory_limit=512M vendor/bin/phpstan analyse"}, "conflict": {"symfony/symfony": "*"}, "extra": {"symfony": {"allow-contrib": false, "require": "6.4.*"}}}