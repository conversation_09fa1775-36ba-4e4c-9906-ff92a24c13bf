#!/bin/bash

# Script pro generování JWT klíčů

JWT_DIR="config/jwt"
PASSPHRASE=${JWT_PASSPHRASE:-"your-jwt-passphrase"}

# Vyt<PERSON>ření adresáře pokud neexistuje
mkdir -p $JWT_DIR

# Generování private klíče
openssl genpkey -out $JWT_DIR/private.pem -aes256 -algorithm rsa -pkcs8 -pass pass:$PASSPHRASE

# Generování public klíče
openssl pkey -in $JWT_DIR/private.pem -passin pass:$PASSPHRASE -out $JWT_DIR/public.pem -pubout

# Nastavení oprávnění
chmod 600 $JWT_DIR/private.pem
chmod 644 $JWT_DIR/public.pem

echo "JWT klíče byly úspěšně vygenerovány v $JWT_DIR/"
