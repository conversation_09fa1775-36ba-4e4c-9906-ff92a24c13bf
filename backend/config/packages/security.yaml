security:
    password_hashers:
        Symfony\Component\Security\Core\User\PasswordAuthenticatedUserInterface: 'auto'
    
    providers:
        app_user_provider:
            entity:
                class: App\Entity\User
                property: email
    
    firewalls:
        dev:
            pattern: ^/(_(profiler|wdt)|css|images|js)/
            security: false
        
        api_login:
            pattern: ^/api/v1/prihlaseni
            stateless: true
            json_login:
                check_path: /api/v1/prihlaseni
                username_path: email
                password_path: password
                success_handler: App\Security\JWTAuthenticationSuccessHandler
                failure_handler: App\Security\JWTAuthenticationFailureHandler

        api_register:
            pattern: ^/api/v1/registrace
            stateless: true
            security: false
        
        api:
            pattern: ^/api/v1
            stateless: true
            jwt: ~
        
        main:
            lazy: true
            provider: app_user_provider

    access_control:
        - { path: ^/api/v1/(registrace|prihlaseni|autentizace/refresh|health|zapomenute-heslo|reset-hesla|overeni-emailu|znovu-odeslat-aktivaci), roles: PUBLIC_ACCESS }
        - { path: ^/api/v1/kategorie, roles: PUBLIC_ACCESS, methods: [GET] }
        - { path: ^/api/v1/predmety, roles: PUBLIC_ACCESS, methods: [GET] }
        - { path: ^/api/v1/recenze, roles: PUBLIC_ACCESS, methods: [GET] }
        - { path: ^/api/v1, roles: IS_AUTHENTICATED_FULLY }

when@test:
    security:
        password_hashers:
            Symfony\Component\Security\Core\User\PasswordAuthenticatedUserInterface:
                algorithm: auto
                cost: 4
                time_cost: 3
                memory_cost: 10
