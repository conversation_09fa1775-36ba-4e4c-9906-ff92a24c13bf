api_platform:
    title: '%env(API_PLATFORM_TITLE)%'
    description: '%env(API_PLATFORM_DESCRIPTION)%'
    version: '%env(API_PLATFORM_VERSION)%'
    
    defaults:
        stateless: true
        cache_headers:
            vary: ['Content-Type', 'Authorization', 'Origin']
        extra_properties:
            standard_put: true
            rfc_7807_compliant_errors: true
    
    mapping:
        paths: ['%kernel.project_dir%/src/Entity']
    
    patch_formats:
        json: ['application/merge-patch+json']
    
    swagger:
        versions: [3]
        api_keys:
            JWT:
                name: Authorization
                type: header
