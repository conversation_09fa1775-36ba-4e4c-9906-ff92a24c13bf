parameters:

services:
    _defaults:
        autowire: true
        autoconfigure: true

    App\:
        resource: '../src/'
        exclude:
            - '../src/DependencyInjection/'
            - '../src/Entity/'
            - '../src/Kernel.php'

    App\Controller\:
        resource: '../src/Controller/'
        tags: ['controller.service_arguments']

    App\EventListener\JWTCreatedListener:
        tags:
            - { name: kernel.event_listener, event: lexik_jwt_authentication.on_jwt_created, method: onJWTCreated }

    App\EventListener\SentryExceptionListener:
        tags:
            - { name: kernel.event_listener, event: kernel.exception, method: onKernelException }

    App\Service\SentryInitializer:
        # Automaticky se inicializuje při vytvoření

    App\Service\MailService:
        arguments:
            $fromEmail: '%env(MAILER_FROM_EMAIL)%'
            $fromName: '%env(MAILER_FROM_NAME)%'
            $frontendBaseUrl: '%env(FRONTEND_BASE_URL)%'
