<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <title>
        {% if status == 'approved' %}
            Rezervace schválena - <PERSON>u<PERSON>na
        {% elseif status == 'rejected' %}
            Rezervace zamítnuta - Pujčovna
        {% else %}
            Aktualizace rezervace - Pujčovna
        {% endif %}
    </title>
    <style>
        body {
            font-family: Arial, sans-serif;
            line-height: 1.6;
            color: #333;
            max-width: 600px;
            margin: 0 auto;
            padding: 20px;
        }
        .header {
            {% if status == 'approved' %}
                background-color: #10b981;
            {% elseif status == 'rejected' %}
                background-color: #ef4444;
            {% else %}
                background-color: #3b82f6;
            {% endif %}
            color: white;
            padding: 20px;
            text-align: center;
            border-radius: 8px 8px 0 0;
        }
        .content {
            background-color: #f9fafb;
            padding: 30px;
            border-radius: 0 0 8px 8px;
        }
        .booking-details {
            background-color: white;
            padding: 20px;
            border-radius: 6px;
            margin: 20px 0;
            {% if status == 'approved' %}
                border-left: 4px solid #10b981;
            {% elseif status == 'rejected' %}
                border-left: 4px solid #ef4444;
            {% else %}
                border-left: 4px solid #3b82f6;
            {% endif %}
        }
        .button {
            display: inline-block;
            {% if status == 'approved' %}
                background-color: #10b981;
            {% elseif status == 'rejected' %}
                background-color: #ef4444;
            {% else %}
                background-color: #3b82f6;
            {% endif %}
            color: white;
            padding: 12px 24px;
            text-decoration: none;
            border-radius: 6px;
            margin: 20px 0;
        }
        .footer {
            margin-top: 30px;
            padding-top: 20px;
            border-top: 1px solid #e5e7eb;
            font-size: 14px;
            color: #6b7280;
        }
    </style>
</head>
<body>
    <div class="header">
        <h1>Pujčovna</h1>
        <p>
            {% if status == 'approved' %}
                Rezervace schválena
            {% elseif status == 'rejected' %}
                Rezervace zamítnuta
            {% else %}
                Aktualizace rezervace
            {% endif %}
        </p>
    </div>
    
    <div class="content">
        <h2>Ahoj {{ borrower.firstName }}!</h2>
        
        {% if status == 'approved' %}
            <p>Vaše rezervace byla schválena!</p>
        {% elseif status == 'rejected' %}
            <p>Vaše rezervace byla bohužel zamítnuta.</p>
        {% else %}
            <p>Stav vaší rezervace byl aktualizován.</p>
        {% endif %}
        
        <div class="booking-details">
            <h3>{{ booking.item.name }}</h3>
            <p><strong>Vlastník:</strong> {{ booking.item.owner.firstName }} {{ booking.item.owner.lastName }}</p>
            <p><strong>Období:</strong> {{ booking.startDate|date('d.m.Y') }} - {{ booking.endDate|date('d.m.Y') }}</p>
            <p><strong>Celková cena:</strong> {{ booking.totalPrice }} Kč</p>
            <p><strong>Stav:</strong> 
                {% if status == 'approved' %}
                    Schváleno
                {% elseif status == 'rejected' %}
                    Zamítnuto
                {% else %}
                    {{ status }}
                {% endif %}
            </p>
            {% if booking.rejectionReason %}
                <p><strong>Důvod zamítnutí:</strong> {{ booking.rejectionReason }}</p>
            {% endif %}
        </div>
        
        {% if status == 'approved' %}
            <p>Můžete kontaktovat vlastníka pro domluvení předání předmětu.</p>
        {% endif %}
        
        <a href="{{ frontendBaseUrl }}/rezervace" class="button">Zobrazit rezervace</a>
        
        <div class="footer">
            <p>S pozdravem,<br>
            Tým Pujčovna</p>
            
            <p><small>Tento email byl odeslán automaticky, neodpovídejte na něj.</small></p>
        </div>
    </div>
</body>
</html>
