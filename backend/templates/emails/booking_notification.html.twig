<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <title>Nová rezervace - Pujčovna</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            line-height: 1.6;
            color: #333;
            max-width: 600px;
            margin: 0 auto;
            padding: 20px;
        }
        .header {
            background-color: #10b981;
            color: white;
            padding: 20px;
            text-align: center;
            border-radius: 8px 8px 0 0;
        }
        .content {
            background-color: #f9fafb;
            padding: 30px;
            border-radius: 0 0 8px 8px;
        }
        .booking-details {
            background-color: white;
            padding: 20px;
            border-radius: 6px;
            margin: 20px 0;
            border-left: 4px solid #10b981;
        }
        .button {
            display: inline-block;
            background-color: #10b981;
            color: white;
            padding: 12px 24px;
            text-decoration: none;
            border-radius: 6px;
            margin: 10px 5px;
        }
        .button.reject {
            background-color: #ef4444;
        }
        .footer {
            margin-top: 30px;
            padding-top: 20px;
            border-top: 1px solid #e5e7eb;
            font-size: 14px;
            color: #6b7280;
        }
    </style>
</head>
<body>
    <div class="header">
        <h1>Pujčovna</h1>
        <p>Nová rezervace</p>
    </div>
    
    <div class="content">
        <h2>Ahoj {{ owner.firstName }}!</h2>
        
        <p>Máte novou rezervaci pro váš předmět:</p>
        
        <div class="booking-details">
            <h3>{{ booking.item.name }}</h3>
            <p><strong>Žadatel:</strong> {{ booking.borrower.firstName }} {{ booking.borrower.lastName }}</p>
            <p><strong>Email:</strong> {{ booking.borrower.email }}</p>
            <p><strong>Telefon:</strong> {{ booking.borrower.phone ?? 'Neuvedeno' }}</p>
            <p><strong>Období:</strong> {{ booking.startDate|date('d.m.Y') }} - {{ booking.endDate|date('d.m.Y') }}</p>
            <p><strong>Celková cena:</strong> {{ booking.totalPrice }} Kč</p>
            {% if booking.message %}
                <p><strong>Zpráva:</strong> {{ booking.message }}</p>
            {% endif %}
        </div>
        
        <p>Pro schválení nebo zamítnutí rezervace se přihlaste do aplikace:</p>

        <a href="{{ frontendBaseUrl }}/rezervace" class="button">Spravovat rezervace</a>
        
        <div class="footer">
            <p>S pozdravem,<br>
            Tým Pujčovna</p>
            
            <p><small>Tento email byl odeslán automaticky, neodpovídejte na něj.</small></p>
        </div>
    </div>
</body>
</html>
