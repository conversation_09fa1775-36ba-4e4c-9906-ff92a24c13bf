<?php

declare(strict_types=1);

namespace DoctrineMigrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

/**
 * Migration for creating item_image table for product image gallery
 */
final class Version20250625120000 extends AbstractMigration
{
    public function getDescription(): string
    {
        return 'Create item_image table for product image gallery functionality';
    }

    public function up(Schema $schema): void
    {
        // Create sequence for item_image table
        $this->addSql(<<<'SQL'
            CREATE SEQUENCE item_image_id_seq INCREMENT BY 1 MINVALUE 1 START 1
        SQL);

        // Create item_image table
        $this->addSql(<<<'SQL'
            CREATE TABLE item_image (
                id INT NOT NULL,
                item_id INT NOT NULL,
                filename VARCHAR(255) NOT NULL,
                original_filename VARCHAR(255) DEFAULT NULL,
                file_size INT DEFAULT NULL,
                mime_type VARCHAR(100) DEFAULT NULL,
                is_primary BOOLEAN NOT NULL DEFAULT FALSE,
                sort_order INT NOT NULL DEFAULT 0,
                created_at TIMESTAMP(0) WITHOUT TIME ZONE NOT NULL,
                PRIMARY KEY(id)
            )
        SQL);

        // Add indexes for better performance
        $this->addSql(<<<'SQL'
            CREATE INDEX IDX_1F1B251E126F525E ON item_image (item_id)
        SQL);

        $this->addSql(<<<'SQL'
            CREATE INDEX IDX_1F1B251E_SORT_ORDER ON item_image (item_id, sort_order)
        SQL);

        $this->addSql(<<<'SQL'
            CREATE INDEX IDX_1F1B251E_PRIMARY ON item_image (item_id, is_primary)
        SQL);

        // Add foreign key constraint
        $this->addSql(<<<'SQL'
            ALTER TABLE item_image ADD CONSTRAINT FK_1F1B251E126F525E 
            FOREIGN KEY (item_id) REFERENCES item (id) ON DELETE CASCADE NOT DEFERRABLE INITIALLY IMMEDIATE
        SQL);

        // Add comment for datetime field
        $this->addSql(<<<'SQL'
            COMMENT ON COLUMN item_image.created_at IS '(DC2Type:datetime_immutable)'
        SQL);
    }

    public function down(Schema $schema): void
    {
        // Drop foreign key constraint
        $this->addSql(<<<'SQL'
            ALTER TABLE item_image DROP CONSTRAINT FK_1F1B251E126F525E
        SQL);

        // Drop table
        $this->addSql(<<<'SQL'
            DROP TABLE item_image
        SQL);

        // Drop sequence
        $this->addSql(<<<'SQL'
            DROP SEQUENCE item_image_id_seq CASCADE
        SQL);
    }
}
