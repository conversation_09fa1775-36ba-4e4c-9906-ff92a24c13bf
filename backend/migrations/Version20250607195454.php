<?php

declare(strict_types=1);

namespace DoctrineMigrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

/**
 * Auto-generated Migration: Please modify to your needs!
 */
final class Version20250607195454 extends AbstractMigration
{
    public function getDescription(): string
    {
        return '';
    }

    public function up(Schema $schema): void
    {
        // this up() migration is auto-generated, please modify it to your needs
        $this->addSql(<<<'SQL'
            ALTER TABLE item ADD street VARCHAR(255) DEFAULT NULL
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE item ADD city VARCHAR(100) DEFAULT NULL
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE item ADD zip_code VARCHAR(10) DEFAULT NULL
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE item ADD latitude NUMERIC(10, 8) DEFAULT NULL
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE item ADD longitude NUMERIC(11, 8) DEFAULT NULL
        SQL);
    }

    public function down(Schema $schema): void
    {
        // this down() migration is auto-generated, please modify it to your needs
        $this->addSql(<<<'SQL'
            CREATE SCHEMA public
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE item DROP street
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE item DROP city
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE item DROP zip_code
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE item DROP latitude
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE item DROP longitude
        SQL);
    }
}
