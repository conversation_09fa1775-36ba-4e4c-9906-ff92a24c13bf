<!DOCTYPE html>
<html lang="cs">
<head>
    <meta charset="utf-8">
    <title>Pujčovna - P2P Sharing Platform</title>
    <meta name="viewport" content="width=device-width,initial-scale=1">
    <meta name="description" content="Moderní platforma pro půjčování věcí mezi sousedy">
    <link rel="icon" href="/favicon.ico">
    <script src="https://cdn.tailwindcss.com"></script>
    <script src="https://unpkg.com/axios/dist/axios.min.js"></script>
</head>
<body class="bg-gray-50">
    <div id="app">
        <div class="min-h-screen flex items-center justify-center">
            <div class="text-center">
                <div class="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4"></div>
                <p class="text-gray-600">Načítání aplikace...</p>
            </div>
        </div>
    </div>

    <script>
        // Základní SPA aplikace pro PHP hosting
        const API_BASE = '/api/v1';

        // Router
        class Router {
            constructor() {
                this.routes = {};
                this.currentRoute = null;
                window.addEventListener('popstate', () => this.handleRoute());
                document.addEventListener('DOMContentLoaded', () => this.handleRoute());
            }

            route(path, handler) {
                this.routes[path] = handler;
            }

            navigate(path) {
                history.pushState(null, null, path);
                this.handleRoute();
            }

            handleRoute() {
                const path = window.location.pathname;
                const handler = this.routes[path] || this.routes['*'];
                if (handler) {
                    handler();
                }
            }
        }

        // API Client
        const api = {
            async get(endpoint) {
                const response = await axios.get(API_BASE + endpoint);
                return response.data;
            },

            async post(endpoint, data) {
                const response = await axios.post(API_BASE + endpoint, data);
                return response.data;
            }
        };

        // Components
        const app = document.getElementById('app');

        function renderHome() {
            app.innerHTML = `
                <div class="min-h-screen">
                    <header class="bg-white shadow">
                        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
                            <div class="flex justify-between items-center py-6">
                                <h1 class="text-3xl font-bold text-gray-900">Pujčovna</h1>
                                <nav class="space-x-4">
                                    <a href="/predmety" class="text-gray-600 hover:text-gray-900">Předměty</a>
                                    <a href="/prihlaseni" class="bg-blue-600 text-white px-4 py-2 rounded-md hover:bg-blue-700">Přihlásit se</a>
                                </nav>
                            </div>
                        </div>
                    </header>

                    <main class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-12">
                        <div class="text-center">
                            <h2 class="text-4xl font-bold text-gray-900 mb-4">
                                Půjčuj a pronajímej věci ve svém okolí
                            </h2>
                            <p class="text-xl text-gray-600 mb-8">
                                Moderní platforma pro sdílení věcí mezi sousedy
                            </p>
                            <div class="space-x-4">
                                <a href="/predmety" class="bg-blue-600 text-white px-6 py-3 rounded-md text-lg hover:bg-blue-700">
                                    Prohlédnout předměty
                                </a>
                                <a href="/registrace" class="border border-blue-600 text-blue-600 px-6 py-3 rounded-md text-lg hover:bg-blue-50">
                                    Registrovat se
                                </a>
                            </div>
                        </div>
                    </main>
                </div>
            `;
        }

        function renderItems() {
            app.innerHTML = `
                <div class="min-h-screen">
                    <header class="bg-white shadow">
                        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
                            <div class="flex justify-between items-center py-6">
                                <h1 class="text-3xl font-bold text-gray-900">
                                    <a href="/">Pujčovna</a>
                                </h1>
                                <nav class="space-x-4">
                                    <a href="/predmety" class="text-blue-600 font-medium">Předměty</a>
                                    <a href="/prihlaseni" class="bg-blue-600 text-white px-4 py-2 rounded-md hover:bg-blue-700">Přihlásit se</a>
                                </nav>
                            </div>
                        </div>
                    </header>

                    <main class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
                        <h2 class="text-2xl font-bold text-gray-900 mb-6">Dostupné předměty</h2>
                        <div id="items-list" class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                            <div class="text-center py-8">
                                <div class="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto mb-4"></div>
                                <p class="text-gray-600">Načítání předmětů...</p>
                            </div>
                        </div>
                    </main>
                </div>
            `;

            // Načtení předmětů z API
            loadItems();
        }

        async function loadItems() {
            try {
                const data = await api.get('/predmety');
                const items = data['hydra:member'] || [];
                const itemsList = document.getElementById('items-list');

                if (items.length === 0) {
                    itemsList.innerHTML = `
                        <div class="col-span-full text-center py-8">
                            <p class="text-gray-600">Zatím nejsou k dispozici žádné předměty</p>
                        </div>
                    `;
                    return;
                }

                itemsList.innerHTML = items.map(item => `
                    <div class="bg-white rounded-lg shadow hover:shadow-md transition-shadow p-6">
                        <h3 class="text-lg font-semibold text-gray-900 mb-2">${item.title}</h3>
                        <p class="text-gray-600 text-sm mb-4">${item.description.substring(0, 100)}...</p>
                        <div class="flex justify-between items-center">
                            <span class="text-lg font-bold text-blue-600">
                                ${item.dailyPrice || 'N/A'} Kč/den
                            </span>
                            <span class="text-sm text-gray-500">
                                ${item.owner?.address?.city || 'Neznámé místo'}
                            </span>
                        </div>
                    </div>
                `).join('');
            } catch (error) {
                console.error('Chyba při načítání předmětů:', error);
                document.getElementById('items-list').innerHTML = `
                    <div class="col-span-full text-center py-8">
                        <p class="text-red-600">Chyba při načítání předmětů</p>
                    </div>
                `;
            }
        }

        function renderLogin() {
            app.innerHTML = `
                <div class="min-h-screen bg-gray-50 flex flex-col justify-center py-12 sm:px-6 lg:px-8">
                    <div class="sm:mx-auto sm:w-full sm:max-w-md">
                        <h2 class="mt-6 text-center text-3xl font-extrabold text-gray-900">
                            Přihlášení do účtu
                        </h2>
                    </div>
                    <div class="mt-8 sm:mx-auto sm:w-full sm:max-w-md">
                        <div class="bg-white py-8 px-4 shadow sm:rounded-lg sm:px-10">
                            <form class="space-y-6">
                                <div>
                                    <label class="block text-sm font-medium text-gray-700">Email</label>
                                    <input type="email" class="mt-1 block w-full border border-gray-300 rounded-md px-3 py-2">
                                </div>
                                <div>
                                    <label class="block text-sm font-medium text-gray-700">Heslo</label>
                                    <input type="password" class="mt-1 block w-full border border-gray-300 rounded-md px-3 py-2">
                                </div>
                                <button type="submit" class="w-full bg-blue-600 text-white py-2 px-4 rounded-md hover:bg-blue-700">
                                    Přihlásit se
                                </button>
                            </form>
                            <div class="mt-6 text-center">
                                <a href="/registrace" class="text-blue-600 hover:text-blue-500">
                                    Nemáte účet? Registrujte se
                                </a>
                            </div>
                        </div>
                    </div>
                </div>
            `;
        }

        function renderRegister() {
            app.innerHTML = `
                <div class="min-h-screen bg-gray-50 flex flex-col justify-center py-12 sm:px-6 lg:px-8">
                    <div class="sm:mx-auto sm:w-full sm:max-w-md">
                        <h2 class="mt-6 text-center text-3xl font-extrabold text-gray-900">
                            Vytvoření účtu
                        </h2>
                    </div>
                    <div class="mt-8 sm:mx-auto sm:w-full sm:max-w-md">
                        <div class="bg-white py-8 px-4 shadow sm:rounded-lg sm:px-10">
                            <form class="space-y-6">
                                <div>
                                    <label class="block text-sm font-medium text-gray-700">Jméno</label>
                                    <input type="text" class="mt-1 block w-full border border-gray-300 rounded-md px-3 py-2">
                                </div>
                                <div>
                                    <label class="block text-sm font-medium text-gray-700">Email</label>
                                    <input type="email" class="mt-1 block w-full border border-gray-300 rounded-md px-3 py-2">
                                </div>
                                <div>
                                    <label class="block text-sm font-medium text-gray-700">Heslo</label>
                                    <input type="password" class="mt-1 block w-full border border-gray-300 rounded-md px-3 py-2">
                                </div>
                                <button type="submit" class="w-full bg-blue-600 text-white py-2 px-4 rounded-md hover:bg-blue-700">
                                    Registrovat se
                                </button>
                            </form>
                            <div class="mt-6 text-center">
                                <a href="/prihlaseni" class="text-blue-600 hover:text-blue-500">
                                    Již máte účet? Přihlaste se
                                </a>
                            </div>
                        </div>
                    </div>
                </div>
            `;
        }

        function renderDashboard() {
            app.innerHTML = `
                <div class="min-h-screen bg-gray-50">
                    <header class="bg-white shadow">
                        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
                            <div class="flex justify-between items-center py-6">
                                <h1 class="text-3xl font-bold text-gray-900">
                                    <a href="/">Pujčovna</a>
                                </h1>
                                <nav class="space-x-4">
                                    <a href="/predmety" class="text-gray-600 hover:text-gray-900">Předměty</a>
                                    <a href="/nastenka" class="text-blue-600 font-medium">Nástěnka</a>
                                    <button class="bg-red-600 text-white px-4 py-2 rounded-md hover:bg-red-700">Odhlásit se</button>
                                </nav>
                            </div>
                        </div>
                    </header>
                    <main class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
                        <h2 class="text-2xl font-bold text-gray-900 mb-6">Nástěnka</h2>
                        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                            <div class="bg-white p-6 rounded-lg shadow">
                                <h3 class="text-lg font-semibold mb-2">Moje předměty</h3>
                                <p class="text-gray-600">Spravujte své nabízené předměty</p>
                            </div>
                            <div class="bg-white p-6 rounded-lg shadow">
                                <h3 class="text-lg font-semibold mb-2">Rezervace</h3>
                                <p class="text-gray-600">Přehled vašich rezervací</p>
                            </div>
                            <div class="bg-white p-6 rounded-lg shadow">
                                <h3 class="text-lg font-semibold mb-2">Profil</h3>
                                <p class="text-gray-600">Upravte své údaje</p>
                            </div>
                        </div>
                    </main>
                </div>
            `;
        }

        function renderDefault() {
            app.innerHTML = `
                <div class="min-h-screen flex items-center justify-center">
                    <div class="text-center">
                        <h1 class="text-4xl font-bold text-gray-900 mb-4">404</h1>
                        <p class="text-gray-600 mb-4">Stránka nebyla nalezena</p>
                        <a href="/" class="bg-blue-600 text-white px-4 py-2 rounded-md hover:bg-blue-700">
                            Zpět na hlavní stránku
                        </a>
                    </div>
                </div>
            `;
        }

        // Router setup
        const router = new Router();
        router.route('/', renderHome);
        router.route('/predmety', renderItems);
        router.route('/prihlaseni', renderLogin);
        router.route('/registrace', renderRegister);
        router.route('/nastenka', renderDashboard);
        router.route('*', renderDefault);

        // Handle clicks on internal links
        document.addEventListener('click', (e) => {
            if (e.target.tagName === 'A' && e.target.href.startsWith(window.location.origin)) {
                e.preventDefault();
                const path = new URL(e.target.href).pathname;
                router.navigate(path);
            }
        });
    </script>
</body>
</html>
