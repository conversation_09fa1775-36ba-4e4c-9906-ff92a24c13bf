# Pujčovna - Public .htaccess
# Symfony aplikace routing

# Zapnutí URL rewriting
RewriteEngine On

# Určení base URL (pokud je aplikace v podsložce)
# RewriteBase /

# Přesměrování na HTTPS (volitelné - odkomentuj pro produkci)
# RewriteCond %{HTTPS} off
# RewriteRule ^(.*)$ https://%{HTTP_HOST}%{REQUEST_URI} [L,R=301]

# Zpracování frontend statických souborů
# Pokud soubor existuje ve frontend/ složce, použij ho
RewriteCond %{REQUEST_FILENAME} !-f
RewriteCond %{REQUEST_FILENAME} !-d
RewriteCond %{DOCUMENT_ROOT}/frontend%{REQUEST_URI} -f
RewriteRule ^(.*)$ frontend/$1 [L]

# API requesty - přesměruj na index.php
RewriteCond %{REQUEST_URI} ^/api/
RewriteCond %{REQUEST_FILENAME} !-f
RewriteRule ^(.*)$ index.php [QSA,L]

# Frontend routing - všechny ostatní requesty na frontend
RewriteCond %{REQUEST_FILENAME} !-f
RewriteCond %{REQUEST_FILENAME} !-d
RewriteCond %{REQUEST_URI} !^/api/
RewriteRule ^(.*)$ frontend/index.html [L]

# Fallback na Symfony pro neexistující soubory
RewriteCond %{REQUEST_FILENAME} !-f
RewriteRule ^(.*)$ index.php [QSA,L]

# Bezpečnost - zakázání přístupu k PHP souborům kromě index.php
<FilesMatch "\.php$">
    <If "%{REQUEST_URI} != '/index.php'">
        Require all denied
    </If>
</FilesMatch>

# Zakázání přístupu k citlivým souborům
<Files ".env*">
    Require all denied
</Files>

<Files "composer.*">
    Require all denied
</Files>

# Optimalizace - komprese
<IfModule mod_deflate.c>
    AddOutputFilterByType DEFLATE text/plain
    AddOutputFilterByType DEFLATE text/html
    AddOutputFilterByType DEFLATE text/xml
    AddOutputFilterByType DEFLATE text/css
    AddOutputFilterByType DEFLATE application/xml
    AddOutputFilterByType DEFLATE application/xhtml+xml
    AddOutputFilterByType DEFLATE application/rss+xml
    AddOutputFilterByType DEFLATE application/javascript
    AddOutputFilterByType DEFLATE application/x-javascript
    AddOutputFilterByType DEFLATE application/json
</IfModule>

# Cache hlavičky
<IfModule mod_expires.c>
    ExpiresActive On
    
    # Frontend statické soubory
    ExpiresByType text/css "access plus 1 year"
    ExpiresByType application/javascript "access plus 1 year"
    ExpiresByType application/x-javascript "access plus 1 year"
    
    # Obrázky
    ExpiresByType image/jpg "access plus 1 month"
    ExpiresByType image/jpeg "access plus 1 month"
    ExpiresByType image/gif "access plus 1 month"
    ExpiresByType image/png "access plus 1 month"
    ExpiresByType image/webp "access plus 1 month"
    ExpiresByType image/svg+xml "access plus 1 month"
    ExpiresByType image/x-icon "access plus 1 year"
    
    # Fonty
    ExpiresByType font/woff "access plus 1 year"
    ExpiresByType font/woff2 "access plus 1 year"
    ExpiresByType application/font-woff "access plus 1 year"
    ExpiresByType application/font-woff2 "access plus 1 year"
    
    # API odpovědi - bez cache
    ExpiresByType application/json "access plus 0 seconds"
    
    # HTML soubory
    ExpiresByType text/html "access plus 1 hour"
    
    ExpiresDefault "access plus 1 day"
</IfModule>

# Bezpečnostní hlavičky
<IfModule mod_headers.c>
    # Základní bezpečnostní hlavičky
    Header always set X-Content-Type-Options nosniff
    Header always set X-Frame-Options SAMEORIGIN
    Header always set X-XSS-Protection "1; mode=block"
    Header always set Referrer-Policy "strict-origin-when-cross-origin"
    
    # Content Security Policy (přizpůsob podle potřeby)
    Header always set Content-Security-Policy "default-src 'self'; script-src 'self' 'unsafe-inline' 'unsafe-eval'; style-src 'self' 'unsafe-inline'; img-src 'self' data: https:; font-src 'self' data:; connect-src 'self'; frame-ancestors 'self';"
    
    # CORS hlavičky pro API
    <If "%{REQUEST_URI} =~ m#^/api/#">
        Header always set Access-Control-Allow-Origin "*"
        Header always set Access-Control-Allow-Methods "GET, POST, PUT, DELETE, OPTIONS"
        Header always set Access-Control-Allow-Headers "Content-Type, Authorization, X-Requested-With"
        Header always set Access-Control-Max-Age "3600"
    </If>
    
    # Cache control pro různé typy souborů
    <FilesMatch "\.(css|js|png|jpg|jpeg|gif|ico|svg|woff|woff2)$">
        Header set Cache-Control "public, max-age=31536000"
    </FilesMatch>
    
    <FilesMatch "\.(html)$">
        Header set Cache-Control "public, max-age=3600"
    </FilesMatch>
    
    <If "%{REQUEST_URI} =~ m#^/api/#">
        Header set Cache-Control "no-cache, no-store, must-revalidate"
        Header set Pragma "no-cache"
        Header set Expires "0"
    </If>
</IfModule>

# Zpracování OPTIONS requestů pro CORS
RewriteCond %{REQUEST_METHOD} OPTIONS
RewriteRule ^(.*)$ $1 [R=200,L]
