<?php

namespace App\Repository;

use App\Entity\ItemImage;
use App\Entity\Item;
use Doctrine\Bundle\DoctrineBundle\Repository\ServiceEntityRepository;
use Doctrine\Persistence\ManagerRegistry;

/**
 * @extends ServiceEntityRepository<ItemImage>
 *
 * @method ItemImage|null find($id, $lockMode = null, $lockVersion = null)
 * @method ItemImage|null findOneBy(array $criteria, array $orderBy = null)
 * @method ItemImage[]    findAll()
 * @method ItemImage[]    findBy(array $criteria, array $orderBy = null, $limit = null, $offset = null)
 */
class ItemImageRepository extends ServiceEntityRepository
{
    public function __construct(ManagerRegistry $registry)
    {
        parent::__construct($registry, ItemImage::class);
    }

    /**
     * Najde všechny obrázky pro daný produkt seřazené podle pořadí
     */
    public function findByItemOrderedBySortOrder(Item $item): array
    {
        return $this->createQueryBuilder('ii')
            ->andWhere('ii.item = :item')
            ->setParameter('item', $item)
            ->orderBy('ii.sortOrder', 'ASC')
            ->addOrderBy('ii.createdAt', 'ASC')
            ->getQuery()
            ->getResult();
    }

    /**
     * Najde hlavní obrázek pro daný produkt
     */
    public function findPrimaryByItem(Item $item): ?ItemImage
    {
        return $this->createQueryBuilder('ii')
            ->andWhere('ii.item = :item')
            ->andWhere('ii.isPrimary = :isPrimary')
            ->setParameter('item', $item)
            ->setParameter('isPrimary', true)
            ->getQuery()
            ->getOneOrNullResult();
    }

    /**
     * Spočítá počet obrázků pro daný produkt
     */
    public function countByItem(Item $item): int
    {
        return $this->createQueryBuilder('ii')
            ->select('COUNT(ii.id)')
            ->andWhere('ii.item = :item')
            ->setParameter('item', $item)
            ->getQuery()
            ->getSingleScalarResult();
    }

    /**
     * Najde nejvyšší pořadí pro daný produkt
     */
    public function findMaxSortOrderByItem(Item $item): int
    {
        $result = $this->createQueryBuilder('ii')
            ->select('MAX(ii.sortOrder)')
            ->andWhere('ii.item = :item')
            ->setParameter('item', $item)
            ->getQuery()
            ->getSingleScalarResult();

        return $result ?? 0;
    }

    /**
     * Resetuje všechny obrázky produktu jako ne-hlavní
     */
    public function resetPrimaryForItem(Item $item): void
    {
        $this->createQueryBuilder('ii')
            ->update()
            ->set('ii.isPrimary', ':isPrimary')
            ->andWhere('ii.item = :item')
            ->setParameter('isPrimary', false)
            ->setParameter('item', $item)
            ->getQuery()
            ->execute();
    }

    /**
     * Aktualizuje pořadí obrázků podle poskytnutého pole ID
     */
    public function updateSortOrder(array $imageIds): void
    {
        foreach ($imageIds as $index => $imageId) {
            $this->createQueryBuilder('ii')
                ->update()
                ->set('ii.sortOrder', ':sortOrder')
                ->andWhere('ii.id = :id')
                ->setParameter('sortOrder', $index)
                ->setParameter('id', $imageId)
                ->getQuery()
                ->execute();
        }
    }
}
