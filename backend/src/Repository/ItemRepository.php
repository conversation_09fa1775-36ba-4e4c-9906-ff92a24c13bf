<?php

namespace App\Repository;

use App\Entity\Item;
use App\Entity\User;
use Doctrine\Bundle\DoctrineBundle\Repository\ServiceEntityRepository;
use Doctrine\Persistence\ManagerRegistry;

class ItemRepository extends ServiceEntityRepository
{
    public function __construct(ManagerRegistry $registry)
    {
        parent::__construct($registry, Item::class);
    }

    public function findAvailable(): array
    {
        return $this->createQueryBuilder('i')
            ->where('i.isAvailable = :available')
            ->setParameter('available', true)
            ->orderBy('i.createdAt', 'DESC')
            ->getQuery()
            ->getResult();
    }

    public function findByCategory(int $categoryId): array
    {
        return $this->createQueryBuilder('i')
            ->where('i.category = :categoryId')
            ->andWhere('i.isAvailable = :available')
            ->setParameter('categoryId', $categoryId)
            ->setParameter('available', true)
            ->orderBy('i.createdAt', 'DESC')
            ->getQuery()
            ->getResult();
    }

    public function searchItems(string $query): array
    {
        return $this->createQueryBuilder('i')
            ->where('i.title LIKE :query OR i.description LIKE :query')
            ->andWhere('i.isAvailable = :available')
            ->setParameter('query', '%'.$query.'%')
            ->setParameter('available', true)
            ->orderBy('i.createdAt', 'DESC')
            ->getQuery()
            ->getResult();
    }

    public function findByOwner(User $owner): array
    {
        return $this->createQueryBuilder('i')
            ->where('i.owner = :owner')
            ->setParameter('owner', $owner)
            ->orderBy('i.createdAt', 'DESC')
            ->getQuery()
            ->getResult();
    }
}
