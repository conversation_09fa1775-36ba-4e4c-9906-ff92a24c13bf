<?php

namespace App\DataFixtures;

use App\Entity\Category;
use Doctrine\Bundle\FixturesBundle\Fixture;
use Doctrine\Persistence\ObjectManager;

class AppFixtures extends Fixture
{
    public function load(ObjectManager $manager): void
    {
        // Vytvoření základních kategorií
        $categories = [
            ['name' => 'Nářadí', 'slug' => 'naradi', 'icon' => 'tools'],
            ['name' => 'Elektronika', 'slug' => 'elektronika', 'icon' => 'laptop'],
            ['name' => 'Domácnost', 'slug' => 'domacnost', 'icon' => 'home'],
            ['name' => 'Sport', 'slug' => 'sport', 'icon' => 'activity'],
            ['name' => 'Zahrada', 'slug' => 'zahrada', 'icon' => 'flower'],
            ['name' => 'Auto a moto', 'slug' => 'auto-moto', 'icon' => 'car'],
            ['name' => '<PERSON><PERSON><PERSON><PERSON><PERSON> n<PERSON>', 'slug' => 'hudebni-nastroje', 'icon' => 'music'],
            ['name' => 'Knihy', 'slug' => 'knihy', 'icon' => 'book'],
        ];

        foreach ($categories as $categoryData) {
            $category = new Category();
            $category->setName($categoryData['name']);
            $category->setSlug($categoryData['slug']);
            $category->setIcon($categoryData['icon']);

            $manager->persist($category);
        }

        $manager->flush();
    }
}
