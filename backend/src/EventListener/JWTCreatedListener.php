<?php

namespace App\EventListener;

use Lexik\Bundle\JWTAuthenticationBundle\Event\JWTCreatedEvent;
use Symfony\Component\HttpFoundation\RequestStack;

class JWTCreatedListener
{
    public function __construct(
        private RequestStack $requestStack
    ) {
    }

    public function onJWTCreated(JWTCreatedEvent $event): void
    {
        $payload = $event->getData();

        // Zkontroluj, jestli je v requestu rememberMe
        $request = $this->requestStack->getCurrentRequest();
        if ($request && $request->getContent()) {
            $requestData = json_decode($request->getContent(), true) ?? [];
            $rememberMe = $requestData['rememberMe'] ?? false;

            if ($rememberMe) {
                // Pro "zapamatuj si mě" - 30 dní (2592000 sekund)
                $payload['exp'] = time() + 2592000;
            } else {
                // Standardní platnost - 1 den (86400 sekund)
                $payload['exp'] = time() + 86400;
            }

            $event->setData($payload);
        }
    }
}
