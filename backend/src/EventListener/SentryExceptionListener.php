<?php

namespace App\EventListener;

use App\Service\SentryService;
use Symfony\Component\HttpKernel\Event\ExceptionEvent;
use Symfony\Component\HttpKernel\Exception\HttpExceptionInterface;
use Symfony\Component\Security\Core\Authentication\Token\Storage\TokenStorageInterface;
use Symfony\Component\Security\Core\User\UserInterface;

class SentryExceptionListener
{
    public function __construct(
        private SentryService $sentryService,
        private TokenStorageInterface $tokenStorage
    ) {
    }

    public function onKernelException(ExceptionEvent $event): void
    {
        $exception = $event->getThrowable();

        // Nepřihlašujeme HTTP chyby 4xx (kromě 500+)
        if ($exception instanceof HttpExceptionInterface) {
            $statusCode = $exception->getStatusCode();
            if ($statusCode < 500) {
                return;
            }
        }

        // Nastavení uživatelského kontextu pokud je uživatel přihlášen
        $token = $this->tokenStorage->getToken();
        if ($token && $token->getUser() instanceof UserInterface) {
            $user = $token->getUser();
            $this->sentryService->setUserContext([
                'id' => method_exists($user, 'getId') ? $user->getId() : 'unknown',
                'email' => method_exists($user, 'getEmail') ? $user->getEmail() : null,
                'username' => $user->getUserIdentifier(),
            ]);
        }

        // Přidání kontextu o requestu
        $request = $event->getRequest();
        $this->sentryService->setContext('request', [
            'url' => $request->getUri(),
            'method' => $request->getMethod(),
            'ip' => $request->getClientIp(),
            'user_agent' => $request->headers->get('User-Agent'),
            'referer' => $request->headers->get('Referer'),
        ]);

        // Logování chyby
        $this->sentryService->logError($exception, [
            'request_id' => $request->headers->get('X-Request-ID'),
            'route' => $request->attributes->get('_route'),
            'controller' => $request->attributes->get('_controller'),
        ]);
    }
}
