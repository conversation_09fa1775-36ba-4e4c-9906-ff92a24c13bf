<?php

namespace App\Service;

use Symfony\Component\DependencyInjection\ParameterBag\ParameterBagInterface;

class ImageService
{
    private const THUMBNAIL_SIZE = 150;
    private const MEDIUM_SIZE = 400;
    private const LARGE_SIZE = 800;
    private const QUALITY = 85;

    public function __construct(
        private ParameterBagInterface $parameterBag
    ) {
    }

    /**
     * Generuje náhledy obrázku v různých velikostech
     */
    public function generateThumbnails(string $originalPath, string $filename, int $itemId): array
    {
        $uploadDir = $this->getUploadDir($itemId);
        $nameWithoutExt = pathinfo($filename, PATHINFO_FILENAME);
        $ext = pathinfo($filename, PATHINFO_EXTENSION);

        $thumbnails = [];

        // Načti původní obrázek
        $originalImage = $this->loadImage($originalPath);
        if (!$originalImage) {
            throw new \Exception('Failed to load original image');
        }

        $originalWidth = imagesx($originalImage);
        $originalHeight = imagesy($originalImage);

        // Generuj thumbnail (150x150)
        $thumbnailPath = $uploadDir . '/' . $nameWithoutExt . '_thumb.' . $ext;
        $this->createResizedImage($originalImage, $thumbnailPath, self::THUMBNAIL_SIZE, self::THUMBNAIL_SIZE, true);
        $thumbnails['thumbnail'] = $thumbnailPath;

        // Generuj medium (400x400)
        $mediumPath = $uploadDir . '/' . $nameWithoutExt . '_medium.' . $ext;
        $this->createResizedImage($originalImage, $mediumPath, self::MEDIUM_SIZE, self::MEDIUM_SIZE, false);
        $thumbnails['medium'] = $mediumPath;

        // Generuj large (800x800)
        $largePath = $uploadDir . '/' . $nameWithoutExt . '_large.' . $ext;
        $this->createResizedImage($originalImage, $largePath, self::LARGE_SIZE, self::LARGE_SIZE, false);
        $thumbnails['large'] = $largePath;

        // Optimalizuj původní obrázek
        $this->optimizeImage($originalImage, $originalPath, $originalWidth, $originalHeight);

        imagedestroy($originalImage);

        return $thumbnails;
    }

    /**
     * Smaže všechny varianty obrázku
     */
    public function deleteImageVariants(string $filename, int $itemId): void
    {
        $uploadDir = $this->getUploadDir($itemId);
        $nameWithoutExt = pathinfo($filename, PATHINFO_FILENAME);
        $ext = pathinfo($filename, PATHINFO_EXTENSION);

        $variants = [
            $uploadDir . '/' . $filename, // original
            $uploadDir . '/' . $nameWithoutExt . '_thumb.' . $ext,
            $uploadDir . '/' . $nameWithoutExt . '_medium.' . $ext,
            $uploadDir . '/' . $nameWithoutExt . '_large.' . $ext,
        ];

        foreach ($variants as $variant) {
            if (file_exists($variant)) {
                unlink($variant);
            }
        }
    }

    /**
     * Zkomprimuje obrázek pro web
     */
    public function compressImage(string $sourcePath, string $destinationPath, int $quality = self::QUALITY): bool
    {
        $image = $this->loadImage($sourcePath);
        if (!$image) {
            return false;
        }

        $result = $this->saveImage($image, $destinationPath, $quality);
        imagedestroy($image);

        return $result;
    }

    private function getUploadDir(int $itemId): string
    {
        return $this->parameterBag->get('kernel.project_dir') . '/public/uploads/items/' . $itemId;
    }

    private function loadImage(string $path)
    {
        $imageInfo = getimagesize($path);
        if (!$imageInfo) {
            return false;
        }

        switch ($imageInfo['mime']) {
            case 'image/jpeg':
                return imagecreatefromjpeg($path);
            case 'image/png':
                return imagecreatefrompng($path);
            case 'image/webp':
                return imagecreatefromwebp($path);
            default:
                return false;
        }
    }

    private function saveImage($image, string $path, int $quality = self::QUALITY): bool
    {
        $ext = strtolower(pathinfo($path, PATHINFO_EXTENSION));

        switch ($ext) {
            case 'jpg':
            case 'jpeg':
                return imagejpeg($image, $path, $quality);
            case 'png':
                // PNG quality je 0-9, převedeme z 0-100
                $pngQuality = (int) ((100 - $quality) / 10);
                return imagepng($image, $path, $pngQuality);
            case 'webp':
                return imagewebp($image, $path, $quality);
            default:
                return false;
        }
    }

    private function createResizedImage($originalImage, string $outputPath, int $maxWidth, int $maxHeight, bool $crop = false): void
    {
        $originalWidth = imagesx($originalImage);
        $originalHeight = imagesy($originalImage);

        if ($crop) {
            // Pro thumbnail - ořízni na čtverec
            $size = min($originalWidth, $originalHeight);
            $x = ($originalWidth - $size) / 2;
            $y = ($originalHeight - $size) / 2;

            $resizedImage = imagecreatetruecolor($maxWidth, $maxHeight);
            $this->preserveTransparency($resizedImage);

            imagecopyresampled(
                $resizedImage, $originalImage,
                0, 0, $x, $y,
                $maxWidth, $maxHeight, $size, $size
            );
        } else {
            // Zachovej poměr stran
            $ratio = min($maxWidth / $originalWidth, $maxHeight / $originalHeight);
            $newWidth = (int) ($originalWidth * $ratio);
            $newHeight = (int) ($originalHeight * $ratio);

            $resizedImage = imagecreatetruecolor($newWidth, $newHeight);
            $this->preserveTransparency($resizedImage);

            imagecopyresampled(
                $resizedImage, $originalImage,
                0, 0, 0, 0,
                $newWidth, $newHeight, $originalWidth, $originalHeight
            );
        }

        $this->saveImage($resizedImage, $outputPath);
        imagedestroy($resizedImage);
    }

    private function optimizeImage($image, string $path, int $width, int $height): void
    {
        // Pokud je obrázek větší než 1200px, zmenši ho
        $maxSize = 1200;
        if ($width > $maxSize || $height > $maxSize) {
            $ratio = min($maxSize / $width, $maxSize / $height);
            $newWidth = (int) ($width * $ratio);
            $newHeight = (int) ($height * $ratio);

            $optimizedImage = imagecreatetruecolor($newWidth, $newHeight);
            $this->preserveTransparency($optimizedImage);

            imagecopyresampled(
                $optimizedImage, $image,
                0, 0, 0, 0,
                $newWidth, $newHeight, $width, $height
            );

            $this->saveImage($optimizedImage, $path);
            imagedestroy($optimizedImage);
        } else {
            // Jen zkomprimuj
            $this->saveImage($image, $path);
        }
    }

    private function preserveTransparency($image): void
    {
        imagealphablending($image, false);
        imagesavealpha($image, true);
        $transparent = imagecolorallocatealpha($image, 255, 255, 255, 127);
        imagefill($image, 0, 0, $transparent);
    }
}
