<?php

namespace App\Service;

use App\Repository\UserRepository;
use Doctrine\ORM\EntityManagerInterface;
use Symfony\Component\PasswordHasher\Hasher\UserPasswordHasherInterface;

class PasswordResetService
{
    public function __construct(
        private EntityManagerInterface $entityManager,
        private UserRepository $userRepository,
        private UserPasswordHasherInterface $passwordHasher,
        private MailService $mailService
    ) {
    }

    public function generateResetToken(string $email): bool
    {
        $user = $this->userRepository->findByEmail($email);

        if (!$user) {
            // Vracíme true i když uživatel neexistuje z bezpečnostních důvodů
            return true;
        }

        // Vygeneruj náhodný token
        $token = bin2hex(random_bytes(32));

        // Nastav token a jeho platnost (1 hodina)
        $user->setResetToken($token);
        $user->setResetTokenExpiresAt(new \DateTimeImmutable('+1 hour'));

        $this->entityManager->flush();

        try {
            // Pošli email
            $this->mailService->sendPasswordResetEmail($user, $token);
        } catch (\Exception $e) {
            throw $e;
        }

        return true;
    }

    public function resetPassword(string $token, string $newPassword): bool
    {
        $user = $this->userRepository->findOneBy(['resetToken' => $token]);

        if (!$user || !$user->isResetTokenValid()) {
            return false;
        }

        // Nastav nové heslo
        $hashedPassword = $this->passwordHasher->hashPassword($user, $newPassword);
        $user->setPassword($hashedPassword);

        // Vymaž reset token
        $user->setResetToken(null);
        $user->setResetTokenExpiresAt(null);

        $this->entityManager->flush();

        return true;
    }

    public function isTokenValid(string $token): bool
    {
        $user = $this->userRepository->findOneBy(['resetToken' => $token]);

        return $user && $user->isResetTokenValid();
    }
}
