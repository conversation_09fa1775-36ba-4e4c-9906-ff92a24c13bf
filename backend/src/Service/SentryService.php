<?php

namespace App\Service;

use function Sentry\captureException;
use function Sentry\captureMessage;
use function Sentry\configureScope;

use Sentry\Severity;
use Sentry\State\Scope;

class SentryService
{
    public function logError(\Throwable $exception, array $context = []): void
    {
        if (!empty($context)) {
            configureScope(function (Scope $scope) use ($context): void {
                $scope->setContext('error_context', $context);
            });
        }

        captureException($exception);
    }

    public function logMessage(string $message, string $level = 'info', array $extra = []): void
    {
        $severity = match ($level) {
            'debug' => Severity::debug(),
            'info' => Severity::info(),
            'warning' => Severity::warning(),
            'error' => Severity::error(),
            'fatal' => Severity::fatal(),
            default => Severity::info(),
        };

        if (!empty($extra)) {
            configureScope(function (Scope $scope) use ($extra): void {
                $scope->setExtra('message_data', $extra);
            });
        }

        captureMessage($message, $severity);
    }

    public function setUserContext(array $user): void
    {
        configureScope(function (Scope $scope) use ($user): void {
            $scope->setUser($user);
        });
    }

    public function clearUserContext(): void
    {
        configureScope(function (Scope $scope): void {
            $scope->setUser(null);
        });
    }

    public function setContext(string $key, array $context): void
    {
        configureScope(function (Scope $scope) use ($key, $context): void {
            $scope->setContext($key, $context);
        });
    }

    public function addBreadcrumb(string $message, string $category = 'default', array $data = []): void
    {
        configureScope(function (Scope $scope) use ($message, $category, $data): void {
            $scope->addBreadcrumb([
                'message' => $message,
                'category' => $category,
                'data' => $data,
                'timestamp' => time(),
            ]);
        });
    }
}
