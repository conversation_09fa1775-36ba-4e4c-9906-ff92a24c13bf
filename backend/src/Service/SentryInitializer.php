<?php

namespace App\Service;

use Symfony\Component\DependencyInjection\Attribute\Autowire;

class SentryInitializer
{
    public function __construct(
        #[Autowire('%env(SENTRY_DSN)%')]
        private string $sentryDsn,
        #[Autowire('%env(SENTRY_ENVIRONMENT)%')]
        private string $sentryEnvironment,
        #[Autowire('%env(float:SENTRY_TRACES_SAMPLE_RATE)%')]
        private float $tracesSampleRate
    ) {
        $this->initializeSentry();
    }

    private function initializeSentry(): void
    {
        if (empty($this->sentryDsn)) {
            return;
        }

        \Sentry\init([
            'dsn' => $this->sentryDsn,
            'environment' => $this->sentryEnvironment,
            'send_default_pii' => true,
            'attach_stacktrace' => true,
            'capture_silenced_errors' => true,
            'traces_sample_rate' => $this->tracesSampleRate,
        ]);
    }
}
