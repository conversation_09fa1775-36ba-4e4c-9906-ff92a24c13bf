<?php

namespace App\Service;

use App\Entity\User;
use Symfony\Bridge\Twig\Mime\TemplatedEmail;
use Symfony\Component\Mailer\MailerInterface;
use Symfony\Component\Mime\Address;

class MailService
{
    public function __construct(
        private MailerInterface $mailer,
        private string $fromEmail,
        private string $fromName,
        private string $frontendBaseUrl
    ) {
    }

    public function sendPasswordResetEmail(User $user, string $resetToken): void
    {
        $resetUrl = sprintf('%s/reset-hesla?token=%s', $this->frontendBaseUrl, $resetToken);

        $email = (new TemplatedEmail())
            ->from(new Address($this->fromEmail, $this->fromName))
            ->to($user->getEmail())
            ->subject('Reset hesla - Pujčovna')
            ->htmlTemplate('emails/password_reset.html.twig')
            ->context([
                'user' => $user,
                'resetToken' => $resetToken,
                'resetUrl' => $resetUrl,
            ]);

        $this->mailer->send($email);
    }

    public function sendBookingNotificationToOwner(User $owner, array $bookingData): void
    {
        $email = (new TemplatedEmail())
            ->from(new Address($this->fromEmail, $this->fromName))
            ->to($owner->getEmail())
            ->subject('Nová rezervace - Pujčovna')
            ->htmlTemplate('emails/booking_notification.html.twig')
            ->context([
                'owner' => $owner,
                'booking' => $bookingData,
                'frontendBaseUrl' => $this->frontendBaseUrl,
            ]);

        $this->mailer->send($email);
    }

    public function sendBookingStatusNotification(User $borrower, array $bookingData, string $status): void
    {
        $subject = match ($status) {
            'approved' => 'Rezervace schválena - Pujčovna',
            'rejected' => 'Rezervace zamítnuta - Pujčovna',
            default => 'Aktualizace rezervace - Pujčovna',
        };

        $email = (new TemplatedEmail())
            ->from(new Address($this->fromEmail, $this->fromName))
            ->to($borrower->getEmail())
            ->subject($subject)
            ->htmlTemplate('emails/booking_status.html.twig')
            ->context([
                'borrower' => $borrower,
                'booking' => $bookingData,
                'status' => $status,
                'frontendBaseUrl' => $this->frontendBaseUrl,
            ]);

        $this->mailer->send($email);
    }

    public function sendEmailVerification(User $user, string $verificationToken): void
    {
        $verificationUrl = sprintf('%s/overeni-emailu?token=%s', $this->frontendBaseUrl, $verificationToken);

        $email = (new TemplatedEmail())
            ->from(new Address($this->fromEmail, $this->fromName))
            ->to($user->getEmail())
            ->subject('Ověření emailu - Pujčovna')
            ->htmlTemplate('emails/email_verification.html.twig')
            ->context([
                'user' => $user,
                'verificationToken' => $verificationToken,
                'verificationUrl' => $verificationUrl,
            ]);

        $this->mailer->send($email);
    }
}
