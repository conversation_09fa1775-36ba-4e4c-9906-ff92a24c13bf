<?php

namespace App\Security;

use App\Entity\User;
use Lexik\Bundle\JWTAuthenticationBundle\Event\AuthenticationSuccessEvent;
use Lexik\Bundle\JWTAuthenticationBundle\Events;
use Lexik\Bundle\JWTAuthenticationBundle\Response\JWTAuthenticationSuccessResponse;
use Lexik\Bundle\JWTAuthenticationBundle\Services\JWTTokenManagerInterface;
use Symfony\Component\HttpFoundation\JsonResponse;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\Security\Core\Authentication\Token\TokenInterface;
use Symfony\Component\Security\Core\User\UserInterface;
use Symfony\Component\Security\Http\Authentication\AuthenticationSuccessHandlerInterface;
use Symfony\Contracts\EventDispatcher\EventDispatcherInterface;

class JWTAuthenticationSuccessHand<PERSON> implements AuthenticationSuccessHandlerInterface
{
    public function __construct(
        private JWTTokenManagerInterface $jwtManager,
        private EventDispatcherInterface $dispatcher
    ) {
    }

    public function onAuthenticationSuccess(Request $request, TokenInterface $token): JsonResponse
    {
        /** @var User $user */
        $user = $token->getUser();

        if (!$user instanceof UserInterface) {
            throw new \RuntimeException('Invalid user type');
        }

        // Kontrola, zda je uživatel ověřený
        if (!$user->isVerified()) {
            return new JsonResponse([
                'error' => 'Email address not verified',
                'message' => 'Váš email ještě nebyl ověřen. Zkontrolujte svou emailovou schránku a klikněte na ověřovací odkaz.',
                'code' => 'EMAIL_NOT_VERIFIED',
            ], 403);
        }

        // Získej data z requestu pro zjištění rememberMe
        $requestData = json_decode($request->getContent(), true) ?? [];
        $rememberMe = $requestData['rememberMe'] ?? false;

        // Vytvoř token - platnost bude nastavena v event listeneru
        $jwt = $this->jwtManager->create($user);

        // Pro refresh token vytvoříme jednoduchý token s uživatelským ID
        // Refresh token bude mít vždy standardní platnost
        $refreshToken = base64_encode(json_encode([
            'user_id' => $user->getId(),
            'type' => 'refresh',
            'exp' => time() + ($rememberMe ? 7776000 : 604800), // 90 dní nebo 7 dní
            'iat' => time(),
        ]));

        $response = new JWTAuthenticationSuccessResponse($jwt);

        // Přidej rememberMe do event data pro použití v event listeneru
        $eventData = [
            'token' => $jwt,
            'rememberMe' => $rememberMe,
        ];
        $event = new AuthenticationSuccessEvent($eventData, $user, $response);
        $this->dispatcher->dispatch($event, Events::AUTHENTICATION_SUCCESS);

        // Přidáme uživatelské údaje a refresh token do response
        $data = [
            'token' => $jwt,
            'refreshToken' => $refreshToken,
            'user' => [
                'id' => $user->getId(),
                'email' => $user->getEmail(),
                'firstName' => $user->getFirstName(),
                'lastName' => $user->getLastName(),
                'isVerified' => $user->isVerified(),
                'roles' => $user->getRoles(),
            ],
        ];

        $response->setData($data);

        return $response;
    }
}
