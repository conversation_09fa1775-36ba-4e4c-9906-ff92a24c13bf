<?php

namespace App\Entity;

use ApiPlatform\Metadata\ApiResource;
use ApiPlatform\Metadata\Delete;
use ApiPlatform\Metadata\Get;
use ApiPlatform\Metadata\Post;
use ApiPlatform\Metadata\Put;
use App\Repository\ItemRepository;
use Doctrine\Common\Collections\ArrayCollection;
use Doctrine\Common\Collections\Collection;
use Doctrine\DBAL\Types\Types;
use Doctrine\ORM\Mapping as ORM;
use Symfony\Component\Serializer\Annotation\Groups;
use Symfony\Component\Validator\Constraints as Assert;

#[ORM\Entity(repositoryClass: ItemRepository::class)]
#[ORM\HasLifecycleCallbacks]
#[ApiResource(
    operations: [
        // new GetCollection(uriTemplate: '/predmety'), // Zakázáno - používá se custom controller
        new Get(uriTemplate: '/predmety/{id}'),
        new Post(uriTemplate: '/predmety', security: "is_granted('ROLE_USER')"),
        new Put(uriTemplate: '/predmety/{id}', security: "is_granted('ROLE_USER') and object.getOwner() == user"),
        new Delete(uriTemplate: '/predmety/{id}', security: "is_granted('ROLE_USER') and object.getOwner() == user"),
    ],
    normalizationContext: ['groups' => ['item:read']],
    denormalizationContext: ['groups' => ['item:write']]
)]
class Item
{
    #[ORM\Id]
    #[ORM\GeneratedValue]
    #[ORM\Column]
    #[Groups(['item:read'])]
    private ?int $id = null;

    #[ORM\Column(length: 255)]
    #[Assert\NotBlank]
    #[Groups(['item:read', 'item:write', 'booking:read'])]
    private ?string $title = null;

    #[ORM\Column(type: Types::TEXT)]
    #[Assert\NotBlank]
    #[Groups(['item:read', 'item:write'])]
    private ?string $description = null;

    #[ORM\Column(type: Types::DECIMAL, precision: 8, scale: 2, nullable: true)]
    #[Groups(['item:read', 'item:write'])]
    private ?string $hourlyPrice = null;

    #[ORM\Column(type: Types::DECIMAL, precision: 8, scale: 2, nullable: true)]
    #[Groups(['item:read', 'item:write'])]
    private ?string $dailyPrice = null;

    #[ORM\Column(type: Types::DECIMAL, precision: 8, scale: 2, nullable: true)]
    #[Groups(['item:read', 'item:write'])]
    private ?string $weeklyPrice = null;

    #[ORM\Column(type: Types::DECIMAL, precision: 8, scale: 2, nullable: true)]
    #[Groups(['item:read', 'item:write'])]
    private ?string $deposit = null;

    #[ORM\Column(type: Types::JSON, nullable: true)]
    #[Groups(['item:read', 'item:write'])]
    private ?array $images = [];

    #[ORM\Column(type: Types::TEXT, nullable: true)]
    #[Groups(['item:read', 'item:write'])]
    private ?string $conditions = null;

    #[ORM\Column(type: Types::DECIMAL, precision: 3, scale: 2, nullable: true)]
    #[Groups(['item:read'])]
    private ?string $rating = null;

    #[ORM\Column]
    #[Groups(['item:read'])]
    private ?int $reviewsCount = 0;

    #[ORM\Column]
    #[Groups(['item:read', 'item:write'])]
    private ?bool $isAvailable = true;

    #[ORM\Column(nullable: true)]
    #[Groups(['item:read', 'item:write'])]
    private ?int $minRentalHours = null;

    #[ORM\Column(nullable: true)]
    #[Groups(['item:read', 'item:write'])]
    private ?int $maxRentalDays = null;

    #[ORM\Column]
    #[Groups(['item:read'])]
    private ?\DateTimeImmutable $createdAt = null;

    #[ORM\Column]
    #[Groups(['item:read'])]
    private ?\DateTimeImmutable $updatedAt = null;

    #[ORM\ManyToOne(inversedBy: 'items')]
    #[ORM\JoinColumn(nullable: false)]
    #[Groups(['item:read', 'item:write'])]
    private ?Category $category = null;

    #[ORM\ManyToOne(inversedBy: 'items')]
    #[ORM\JoinColumn(nullable: false)]
    #[Groups(['item:read', 'booking:read'])]
    private ?User $owner = null;

    #[ORM\OneToMany(mappedBy: 'item', targetEntity: Booking::class, orphanRemoval: true)]
    private Collection $bookings;

    #[ORM\OneToMany(mappedBy: 'item', targetEntity: Review::class)]
    private Collection $reviews;

    #[ORM\OneToMany(mappedBy: 'item', targetEntity: ItemImage::class, orphanRemoval: true)]
    #[Groups(['item:read'])]
    private Collection $itemImages;

    public function __construct()
    {
        $this->bookings = new ArrayCollection();
        $this->reviews = new ArrayCollection();
        $this->itemImages = new ArrayCollection();
        $this->createdAt = new \DateTimeImmutable();
        $this->updatedAt = new \DateTimeImmutable();
    }

    public function getId(): ?int
    {
        return $this->id;
    }

    public function getTitle(): ?string
    {
        return $this->title;
    }

    public function setTitle(string $title): static
    {
        $this->title = $title;

        return $this;
    }

    public function getDescription(): ?string
    {
        return $this->description;
    }

    public function setDescription(string $description): static
    {
        $this->description = $description;

        return $this;
    }

    public function getHourlyPrice(): ?string
    {
        return $this->hourlyPrice;
    }

    public function setHourlyPrice(?string $hourlyPrice): static
    {
        $this->hourlyPrice = $hourlyPrice;

        return $this;
    }

    public function getDailyPrice(): ?string
    {
        return $this->dailyPrice;
    }

    public function setDailyPrice(?string $dailyPrice): static
    {
        $this->dailyPrice = $dailyPrice;

        return $this;
    }

    public function getWeeklyPrice(): ?string
    {
        return $this->weeklyPrice;
    }

    public function setWeeklyPrice(?string $weeklyPrice): static
    {
        $this->weeklyPrice = $weeklyPrice;

        return $this;
    }

    public function getDeposit(): ?string
    {
        return $this->deposit;
    }

    public function setDeposit(?string $deposit): static
    {
        $this->deposit = $deposit;

        return $this;
    }

    public function getImages(): ?array
    {
        return $this->images;
    }

    public function setImages(?array $images): static
    {
        $this->images = $images;

        return $this;
    }

    public function getConditions(): ?string
    {
        return $this->conditions;
    }

    public function setConditions(?string $conditions): static
    {
        $this->conditions = $conditions;

        return $this;
    }

    public function getRating(): ?string
    {
        return $this->rating;
    }

    public function setRating(?string $rating): static
    {
        $this->rating = $rating;

        return $this;
    }

    public function getReviewsCount(): ?int
    {
        return $this->reviewsCount;
    }

    public function setReviewsCount(int $reviewsCount): static
    {
        $this->reviewsCount = $reviewsCount;

        return $this;
    }

    public function isAvailable(): ?bool
    {
        return $this->isAvailable;
    }

    public function setAvailable(bool $isAvailable): static
    {
        $this->isAvailable = $isAvailable;

        return $this;
    }

    public function getMinRentalHours(): ?int
    {
        return $this->minRentalHours;
    }

    public function setMinRentalHours(?int $minRentalHours): static
    {
        $this->minRentalHours = $minRentalHours;

        return $this;
    }

    public function getMaxRentalDays(): ?int
    {
        return $this->maxRentalDays;
    }

    public function setMaxRentalDays(?int $maxRentalDays): static
    {
        $this->maxRentalDays = $maxRentalDays;

        return $this;
    }

    public function getCreatedAt(): ?\DateTimeImmutable
    {
        return $this->createdAt;
    }

    public function setCreatedAt(\DateTimeImmutable $createdAt): static
    {
        $this->createdAt = $createdAt;

        return $this;
    }

    public function getUpdatedAt(): ?\DateTimeImmutable
    {
        return $this->updatedAt;
    }

    public function setUpdatedAt(\DateTimeImmutable $updatedAt): static
    {
        $this->updatedAt = $updatedAt;

        return $this;
    }

    public function getCategory(): ?Category
    {
        return $this->category;
    }

    public function setCategory(?Category $category): static
    {
        $this->category = $category;

        return $this;
    }

    public function getOwner(): ?User
    {
        return $this->owner;
    }

    public function setOwner(?User $owner): static
    {
        $this->owner = $owner;

        return $this;
    }

    public function getBookings(): Collection
    {
        return $this->bookings;
    }

    public function addBooking(Booking $booking): static
    {
        if (!$this->bookings->contains($booking)) {
            $this->bookings->add($booking);
            $booking->setItem($this);
        }

        return $this;
    }

    public function removeBooking(Booking $booking): static
    {
        if ($this->bookings->removeElement($booking)) {
            if ($booking->getItem() === $this) {
                $booking->setItem(null);
            }
        }

        return $this;
    }

    public function getReviews(): Collection
    {
        return $this->reviews;
    }

    public function addReview(Review $review): static
    {
        if (!$this->reviews->contains($review)) {
            $this->reviews->add($review);
            $review->setItem($this);
        }

        return $this;
    }

    public function removeReview(Review $review): static
    {
        if ($this->reviews->removeElement($review)) {
            if ($review->getItem() === $this) {
                $review->setItem(null);
            }
        }

        return $this;
    }

    /**
     * @return Collection<int, ItemImage>
     */
    public function getItemImages(): Collection
    {
        return $this->itemImages;
    }

    public function addItemImage(ItemImage $itemImage): static
    {
        if (!$this->itemImages->contains($itemImage)) {
            $this->itemImages->add($itemImage);
            $itemImage->setItem($this);
        }

        return $this;
    }

    public function removeItemImage(ItemImage $itemImage): static
    {
        if ($this->itemImages->removeElement($itemImage)) {
            if ($itemImage->getItem() === $this) {
                $itemImage->setItem(null);
            }
        }

        return $this;
    }

    /**
     * Získá hlavní obrázek produktu
     */
    public function getPrimaryImage(): ?ItemImage
    {
        foreach ($this->itemImages as $image) {
            if ($image->isPrimary()) {
                return $image;
            }
        }

        return $this->itemImages->first() ?: null;
    }

    /**
     * Získá všechny obrázky seřazené podle pořadí
     */
    public function getOrderedImages(): array
    {
        $images = $this->itemImages->toArray();
        usort($images, function (ItemImage $a, ItemImage $b) {
            return $a->getSortOrder() <=> $b->getSortOrder();
        });

        return $images;
    }

    #[ORM\PreUpdate]
    public function preUpdate(): void
    {
        $this->updatedAt = new \DateTimeImmutable();
    }
}
