<?php

namespace App\Entity;

use ApiPlatform\Metadata\ApiResource;
use ApiPlatform\Metadata\Delete;
use ApiPlatform\Metadata\Get;
use ApiPlatform\Metadata\Post;
use ApiPlatform\Metadata\Put;
use App\Repository\ItemImageRepository;
use Doctrine\DBAL\Types\Types;
use Doctrine\ORM\Mapping as ORM;
use Symfony\Component\Serializer\Annotation\Groups;
use Symfony\Component\Validator\Constraints as Assert;

#[ORM\Entity(repositoryClass: ItemImageRepository::class)]
#[ORM\HasLifecycleCallbacks]
#[ApiResource(
    operations: [
        new Get(uriTemplate: '/predmety/{itemId}/images/{id}', security: "is_granted('ROLE_USER')"),
        new Post(uriTemplate: '/predmety/{itemId}/images', security: "is_granted('ROLE_USER')"),
        new Put(uriTemplate: '/predmety/{itemId}/images/{id}', security: "is_granted('ROLE_USER')"),
        new Delete(uriTemplate: '/predmety/{itemId}/images/{id}', security: "is_granted('ROLE_USER')"),
    ],
    normalizationContext: ['groups' => ['item_image:read']],
    denormalizationContext: ['groups' => ['item_image:write']]
)]
class ItemImage
{
    #[ORM\Id]
    #[ORM\GeneratedValue]
    #[ORM\Column]
    #[Groups(['item_image:read', 'item:read'])]
    private ?int $id = null;

    #[ORM\Column(length: 255)]
    #[Assert\NotBlank]
    #[Groups(['item_image:read', 'item_image:write', 'item:read'])]
    private ?string $filename = null;

    #[ORM\Column(length: 255, nullable: true)]
    #[Groups(['item_image:read', 'item_image:write'])]
    private ?string $originalFilename = null;

    #[ORM\Column(nullable: true)]
    #[Groups(['item_image:read', 'item_image:write'])]
    private ?int $fileSize = null;

    #[ORM\Column(length: 100, nullable: true)]
    #[Groups(['item_image:read', 'item_image:write'])]
    private ?string $mimeType = null;

    #[ORM\Column]
    #[Groups(['item_image:read', 'item_image:write', 'item:read'])]
    private ?bool $isPrimary = false;

    #[ORM\Column]
    #[Groups(['item_image:read', 'item_image:write', 'item:read'])]
    private ?int $sortOrder = 0;

    #[ORM\Column]
    #[Groups(['item_image:read'])]
    private ?\DateTimeImmutable $createdAt = null;

    #[ORM\ManyToOne(inversedBy: 'itemImages')]
    #[ORM\JoinColumn(nullable: false)]
    #[Groups(['item_image:read'])]
    private ?Item $item = null;

    public function __construct()
    {
        $this->createdAt = new \DateTimeImmutable();
    }

    #[ORM\PrePersist]
    public function setCreatedAtValue(): void
    {
        $this->createdAt = new \DateTimeImmutable();
    }

    public function getId(): ?int
    {
        return $this->id;
    }

    public function getFilename(): ?string
    {
        return $this->filename;
    }

    public function setFilename(string $filename): static
    {
        $this->filename = $filename;
        return $this;
    }

    public function getOriginalFilename(): ?string
    {
        return $this->originalFilename;
    }

    public function setOriginalFilename(?string $originalFilename): static
    {
        $this->originalFilename = $originalFilename;
        return $this;
    }

    public function getFileSize(): ?int
    {
        return $this->fileSize;
    }

    public function setFileSize(?int $fileSize): static
    {
        $this->fileSize = $fileSize;
        return $this;
    }

    public function getMimeType(): ?string
    {
        return $this->mimeType;
    }

    public function setMimeType(?string $mimeType): static
    {
        $this->mimeType = $mimeType;
        return $this;
    }

    public function isPrimary(): ?bool
    {
        return $this->isPrimary;
    }

    public function setIsPrimary(bool $isPrimary): static
    {
        $this->isPrimary = $isPrimary;
        return $this;
    }

    public function getSortOrder(): ?int
    {
        return $this->sortOrder;
    }

    public function setSortOrder(int $sortOrder): static
    {
        $this->sortOrder = $sortOrder;
        return $this;
    }

    public function getCreatedAt(): ?\DateTimeImmutable
    {
        return $this->createdAt;
    }

    public function setCreatedAt(\DateTimeImmutable $createdAt): static
    {
        $this->createdAt = $createdAt;
        return $this;
    }

    public function getItem(): ?Item
    {
        return $this->item;
    }

    public function setItem(?Item $item): static
    {
        $this->item = $item;
        return $this;
    }

    /**
     * Generuje URL cesty pro různé velikosti obrázků
     */
    public function getUrls(): array
    {
        $baseUrl = '/uploads/items/' . $this->item?->getId() . '/';
        $nameWithoutExt = pathinfo($this->filename, PATHINFO_FILENAME);
        $ext = pathinfo($this->filename, PATHINFO_EXTENSION);

        return [
            'thumbnail' => $baseUrl . $nameWithoutExt . '_thumb.' . $ext,
            'medium' => $baseUrl . $nameWithoutExt . '_medium.' . $ext,
            'large' => $baseUrl . $nameWithoutExt . '_large.' . $ext,
            'original' => $baseUrl . $this->filename,
        ];
    }
}
