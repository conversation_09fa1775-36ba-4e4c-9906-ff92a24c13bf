<?php

namespace App\Controller;

use Symfony\Bundle\FrameworkBundle\Controller\AbstractController;
use Symfony\Component\HttpFoundation\BinaryFileResponse;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\HttpKernel\Exception\NotFoundHttpException;
use Symfony\Component\Routing\Annotation\Route;

class FrontendController extends AbstractController
{
    private string $frontendPath;

    public function __construct()
    {
        $this->frontendPath = dirname(__DIR__, 2).'/public/frontend';
    }

    /**
     * Obsluha hlavn<PERSON> stránky a všech frontend rout.
     */
    #[Route('/', name: 'frontend_index', priority: -1)]
    #[Route('/prihlaseni', name: 'frontend_login')]
    #[Route('/registrace', name: 'frontend_register')]
    #[Route('/predmety', name: 'frontend_items')]
    #[Route('/predmety/{id}', name: 'frontend_item_detail', requirements: ['id' => '\d+'])]
    #[Route('/nastenka', name: 'frontend_dashboard')]
    #[Route('/profil', name: 'frontend_profile')]
    #[Route('/moje-predmety', name: 'frontend_my_items')]
    #[Route('/pujcovani', name: 'frontend_bookings')]
    #[Route('/kategorie', name: 'frontend_categories')]
    #[Route('/o-nas', name: 'frontend_about')]
    #[Route('/jak-to-funguje', name: 'frontend_how_it_works')]
    #[Route('/kontakt', name: 'frontend_contact')]
    #[Route('/{path}', name: 'frontend_catch_all', requirements: ['path' => '.*'], priority: -10)]
    public function index(): Response
    {
        $indexPath = $this->frontendPath.'/index.html';

        if (!file_exists($indexPath)) {
            throw new NotFoundHttpException('Frontend aplikace nebyla nalezena. Spusťte build frontendu.');
        }

        $content = file_get_contents($indexPath);

        return new Response($content, 200, [
            'Content-Type' => 'text/html; charset=UTF-8',
            'Cache-Control' => 'no-cache, no-store, must-revalidate',
            'Pragma' => 'no-cache',
            'Expires' => '0',
        ]);
    }

    /**
     * Obsluha statických souborů frontendu.
     */
    #[Route('/_next/{path}', name: 'frontend_next_assets', requirements: ['path' => '.+'])]
    #[Route('/static/{path}', name: 'frontend_static_assets', requirements: ['path' => '.+'])]

    #[Route('/favicon.ico', name: 'frontend_favicon')]
    #[Route('/manifest.json', name: 'frontend_manifest')]
    #[Route('/robots.txt', name: 'frontend_robots')]
    #[Route('/icon.svg', name: 'frontend_icon')]
    public function assets(): BinaryFileResponse
    {
        $requestUri = $_SERVER['REQUEST_URI'];
        $filePath = $this->frontendPath.$requestUri;

        if (!file_exists($filePath) || !is_file($filePath)) {
            throw new NotFoundHttpException('Soubor nebyl nalezen.');
        }

        $mimeType = $this->getMimeType($filePath);
        $response = new BinaryFileResponse($filePath);
        $response->headers->set('Content-Type', $mimeType);

        // Cache pro statické soubory
        if (str_contains($requestUri, '/_next/') || str_contains($requestUri, '/static/')) {
            $response->headers->set('Cache-Control', 'public, max-age=31536000, immutable');
        }

        return $response;
    }

    private function getMimeType(string $filePath): string
    {
        $extension = pathinfo($filePath, PATHINFO_EXTENSION);

        return match ($extension) {
            'js' => 'application/javascript',
            'css' => 'text/css',
            'png' => 'image/png',
            'jpg', 'jpeg' => 'image/jpeg',
            'gif' => 'image/gif',
            'svg' => 'image/svg+xml',
            'ico' => 'image/x-icon',
            'json' => 'application/json',
            'txt' => 'text/plain',
            'html' => 'text/html',
            'woff' => 'font/woff',
            'woff2' => 'font/woff2',
            'ttf' => 'font/ttf',
            'eot' => 'application/vnd.ms-fontobject',
            default => 'application/octet-stream',
        };
    }
}
