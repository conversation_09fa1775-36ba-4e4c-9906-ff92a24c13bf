<?php

namespace App\Controller;

use App\Entity\Item;
use App\Entity\User;
use App\Repository\CategoryRepository;
use App\Repository\ItemRepository;
use Doctrine\ORM\EntityManagerInterface;
use Symfony\Bundle\FrameworkBundle\Controller\AbstractController;
use Symfony\Component\HttpFoundation\JsonResponse;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\Routing\Attribute\Route;
use Symfony\Component\Security\Http\Attribute\IsGranted;
use Symfony\Component\Validator\Validator\ValidatorInterface;

#[Route('/api/v1')]
class ItemController extends AbstractController
{
    public function __construct(
        private ItemRepository $itemRepository,
        private CategoryRepository $categoryRepository,
        private EntityManagerInterface $entityManager,
        private ValidatorInterface $validator
    ) {
    }

    #[Route('/predmety', name: 'items_list', methods: ['GET'])]
    public function list(Request $request): JsonResponse
    {
        $page = max(1, (int) $request->query->get('page', 1));
        $limit = min(50, max(1, (int) $request->query->get('limit', 10)));
        $search = $request->query->get('search');
        $category = $request->query->get('category');
        $minPrice = $request->query->get('minPrice');
        $maxPrice = $request->query->get('maxPrice');
        // TODO: Implementovat geografické filtrování
        // $latitude = $request->query->get('latitude');
        // $longitude = $request->query->get('longitude');
        // $radius = $request->query->get('radius', 10);
        $available = $request->query->get('available');
        $excludeOwn = $request->query->get('excludeOwn', false);
        $sortBy = $request->query->get('sortBy', 'createdAt');
        $sortOrder = $request->query->get('sortOrder', 'DESC');

        $offset = ($page - 1) * $limit;

        $queryBuilder = $this->itemRepository->createQueryBuilder('i')
            ->leftJoin('i.owner', 'u')
            ->leftJoin('u.address', 'a')
            ->leftJoin('i.category', 'c');

        // Filtrování
        if ($search) {
            $queryBuilder->andWhere('i.title LIKE :search OR i.description LIKE :search')
                ->setParameter('search', '%'.$search.'%');
        }

        if ($category) {
            $queryBuilder->andWhere('i.category = :category')
                ->setParameter('category', $category);
        }

        if ($minPrice) {
            $queryBuilder->andWhere('i.dailyPrice >= :minPrice')
                ->setParameter('minPrice', $minPrice);
        }

        if ($maxPrice) {
            $queryBuilder->andWhere('i.dailyPrice <= :maxPrice')
                ->setParameter('maxPrice', $maxPrice);
        }

        if (null !== $available) {
            $queryBuilder->andWhere('i.isAvailable = :available')
                ->setParameter('available', (bool) $available);
        }

        // Vyloučit vlastní předměty pokud je uživatel přihlášen a excludeOwn je true
        if ($excludeOwn && $this->getUser()) {
            $queryBuilder->andWhere('i.owner != :currentUser')
                ->setParameter('currentUser', $this->getUser());
        }

        // Řazení
        $allowedSortFields = ['title', 'dailyPrice', 'createdAt', 'rating'];
        if (in_array($sortBy, $allowedSortFields)) {
            $queryBuilder->orderBy('i.'.$sortBy, 'ASC' === $sortOrder ? 'ASC' : 'DESC');
        }

        // Celkový počet - musíme odstranit ORDER BY pro COUNT dotaz
        $totalQuery = clone $queryBuilder;
        $totalQuery->select('COUNT(i.id)');
        // Odstraníme všechny ORDER BY klauzule pro COUNT dotaz
        $totalQuery->resetDQLPart('orderBy');
        $total = $totalQuery->getQuery()->getSingleScalarResult();

        // Stránkování
        $items = $queryBuilder
            ->setFirstResult($offset)
            ->setMaxResults($limit)
            ->getQuery()
            ->getResult();

        // Serializace dat
        $data = array_map(function (Item $item) {
            return [
                'id' => $item->getId(),
                'title' => $item->getTitle(),
                'description' => $item->getDescription(),
                'price' => [
                    'daily' => $item->getDailyPrice() ? (float) $item->getDailyPrice() : null,
                    'weekly' => $item->getWeeklyPrice() ? (float) $item->getWeeklyPrice() : null,
                    'monthly' => null, // TODO: přidat monthly price do entity
                ],
                'category' => $item->getCategory() ? [
                    'id' => $item->getCategory()->getId(),
                    'name' => $item->getCategory()->getName(),
                ] : null,
                'location' => $item->getOwner()->getAddress() ? [
                    'city' => $item->getOwner()->getAddress()->getCity(),
                ] : null,
                'owner' => [
                    'id' => $item->getOwner()->getId(),
                    'firstName' => $item->getOwner()->getFirstName(),
                    'lastName' => $item->getOwner()->getLastName(),
                    'rating' => $item->getOwner()->getRating() ? (float) $item->getOwner()->getRating() : null,
                ],
                'images' => [], // TODO: implementovat obrázky
                'isAvailable' => $item->isAvailable(),
                'rating' => $item->getRating() ? (float) $item->getRating() : null,
                'reviewsCount' => $item->getReviewsCount(),
                'createdAt' => $item->getCreatedAt()?->format('c'),
                'updatedAt' => $item->getUpdatedAt()?->format('c'),
            ];
        }, $items);

        return new JsonResponse([
            'data' => $data,
            'total' => (int) $total,
            'page' => $page,
            'limit' => $limit,
            'totalPages' => (int) ceil($total / $limit),
        ]);
    }

    #[Route('/uzivatele/predmety', name: 'user_items', methods: ['GET'])]
    #[IsGranted('ROLE_USER')]
    public function getUserItems(): JsonResponse
    {
        /** @var User $user */
        $user = $this->getUser();

        if (!$user) {
            return new JsonResponse(['error' => 'User not found'], 404);
        }

        $items = $this->itemRepository->findByOwner($user);

        $data = [];
        foreach ($items as $item) {
            $data[] = [
                'id' => $item->getId(),
                'title' => $item->getTitle(),
                'description' => $item->getDescription(),
                'dailyPrice' => $item->getDailyPrice(),
                'pricePerDay' => $item->getDailyPrice() ? (float) $item->getDailyPrice() : null,
                'deposit' => $item->getDeposit(),
                'category' => [
                    'id' => $item->getCategory()?->getId(),
                    'name' => $item->getCategory()?->getName(),
                ],
                'conditions' => $item->getConditions(),
                'condition' => $item->getConditions(),
                'isAvailable' => $item->isAvailable(),
                'rating' => $item->getRating(),
                'reviewsCount' => $item->getReviewsCount(),
                'createdAt' => $item->getCreatedAt()?->format('c'),
                'updatedAt' => $item->getUpdatedAt()?->format('c'),
                'images' => $item->getImages() ?? [],
            ];
        }

        return new JsonResponse([
            'data' => $data,
            'total' => count($data),
            'page' => 1,
            'limit' => count($data),
        ]);
    }

    #[Route('/predmety', name: 'items_create', methods: ['POST'])]
    #[IsGranted('ROLE_USER')]
    public function create(Request $request): JsonResponse
    {
        $data = json_decode($request->getContent(), true);

        if (!$data) {
            return new JsonResponse(['error' => 'Invalid JSON'], 400);
        }

        $item = new Item();
        $item->setTitle($data['title'] ?? '');
        $item->setDescription($data['description'] ?? '');

        // Nastavení ceny
        if (isset($data['pricePerDay'])) {
            $item->setDailyPrice((string) $data['pricePerDay']);
        }

        if (isset($data['deposit'])) {
            $item->setDeposit((string) $data['deposit']);
        }

        // Nastavení kategorie
        if (isset($data['categoryId'])) {
            $category = $this->categoryRepository->find($data['categoryId']);
            if ($category) {
                $item->setCategory($category);
            }
        }

        // Nastavení vlastníka
        $item->setOwner($this->getUser());

        // Nastavení dalších polí
        if (isset($data['condition'])) {
            $item->setConditions($data['condition']);
        }

        // Validace
        $errors = $this->validator->validate($item);
        if (count($errors) > 0) {
            $errorMessages = [];
            foreach ($errors as $error) {
                $errorMessages[] = [
                    'field' => $error->getPropertyPath(),
                    'message' => $error->getMessage(),
                ];
            }

            return new JsonResponse(['error' => 'Validation failed', 'violations' => $errorMessages], 422);
        }

        try {
            $this->entityManager->persist($item);
            $this->entityManager->flush();

            return new JsonResponse([
                'id' => $item->getId(),
                'title' => $item->getTitle(),
                'description' => $item->getDescription(),
                'dailyPrice' => $item->getDailyPrice(),
                'deposit' => $item->getDeposit(),
                'category' => [
                    'id' => $item->getCategory()?->getId(),
                    'name' => $item->getCategory()?->getName(),
                ],
                'owner' => [
                    'id' => $item->getOwner()?->getId(),
                    'firstName' => $item->getOwner()?->getFirstName(),
                    'lastName' => $item->getOwner()?->getLastName(),
                ],
                'conditions' => $item->getConditions(),
                'isAvailable' => $item->isAvailable(),
                'createdAt' => $item->getCreatedAt()?->format('c'),
                'updatedAt' => $item->getUpdatedAt()?->format('c'),
            ], 201);
        } catch (\Exception $e) {
            return new JsonResponse(['error' => 'Failed to create item: '.$e->getMessage()], 500);
        }
    }

    #[Route('/predmety/{id}', name: 'items_update', methods: ['PUT'])]
    #[IsGranted('ROLE_USER')]
    public function update(int $id, Request $request): JsonResponse
    {
        $item = $this->itemRepository->find($id);

        if (!$item) {
            return new JsonResponse(['error' => 'Item not found'], 404);
        }

        // Zkontroluj, jestli je uživatel vlastník
        if ($item->getOwner() !== $this->getUser()) {
            return new JsonResponse(['error' => 'Access denied'], 403);
        }

        $data = json_decode($request->getContent(), true);

        if (!$data) {
            return new JsonResponse(['error' => 'Invalid JSON'], 400);
        }

        // Aktualizuj data
        if (isset($data['title'])) {
            $item->setTitle($data['title']);
        }

        if (isset($data['description'])) {
            $item->setDescription($data['description']);
        }

        if (isset($data['pricePerDay'])) {
            $item->setDailyPrice((string) $data['pricePerDay']);
        }

        if (isset($data['deposit'])) {
            $item->setDeposit((string) $data['deposit']);
        }

        if (isset($data['categoryId'])) {
            $category = $this->categoryRepository->find($data['categoryId']);
            if ($category) {
                $item->setCategory($category);
            }
        }

        if (isset($data['condition'])) {
            $item->setConditions($data['condition']);
        }

        // Validace
        $errors = $this->validator->validate($item);
        if (count($errors) > 0) {
            $errorMessages = [];
            foreach ($errors as $error) {
                $errorMessages[] = [
                    'field' => $error->getPropertyPath(),
                    'message' => $error->getMessage(),
                ];
            }

            return new JsonResponse(['error' => 'Validation failed', 'violations' => $errorMessages], 422);
        }

        try {
            $this->entityManager->flush();

            return new JsonResponse([
                'id' => $item->getId(),
                'title' => $item->getTitle(),
                'description' => $item->getDescription(),
                'dailyPrice' => $item->getDailyPrice(),
                'deposit' => $item->getDeposit(),
                'category' => [
                    'id' => $item->getCategory()?->getId(),
                    'name' => $item->getCategory()?->getName(),
                ],
                'owner' => [
                    'id' => $item->getOwner()?->getId(),
                    'firstName' => $item->getOwner()?->getFirstName(),
                    'lastName' => $item->getOwner()?->getLastName(),
                ],
                'conditions' => $item->getConditions(),
                'isAvailable' => $item->isAvailable(),
                'createdAt' => $item->getCreatedAt()?->format('c'),
                'updatedAt' => $item->getUpdatedAt()?->format('c'),
            ]);
        } catch (\Exception $e) {
            return new JsonResponse(['error' => 'Failed to update item: '.$e->getMessage()], 500);
        }
    }

    #[Route('/predmety/{id}', name: 'items_get', methods: ['GET'])]
    public function get(int $id): JsonResponse
    {
        $item = $this->itemRepository->find($id);

        if (!$item) {
            return new JsonResponse(['error' => 'Item not found'], 404);
        }

        return new JsonResponse([
            'id' => $item->getId(),
            'title' => $item->getTitle(),
            'description' => $item->getDescription(),
            'dailyPrice' => $item->getDailyPrice(),
            'deposit' => $item->getDeposit(),
            'price' => [
                'daily' => $item->getDailyPrice() ? (float) $item->getDailyPrice() : null,
                'weekly' => $item->getWeeklyPrice() ? (float) $item->getWeeklyPrice() : null,
                'monthly' => null,
            ],
            'category' => [
                'id' => $item->getCategory()?->getId(),
                'name' => $item->getCategory()?->getName(),
            ],
            'location' => [
                'city' => $item->getOwner()?->getAddress()?->getCity() ?? 'Neuvedeno',
            ],
            'owner' => [
                'id' => $item->getOwner()?->getId(),
                'firstName' => $item->getOwner()?->getFirstName(),
                'lastName' => $item->getOwner()?->getLastName(),
                'rating' => null,
            ],
            'images' => $item->getImages() ?? [],
            'conditions' => $item->getConditions(),
            'isAvailable' => $item->isAvailable(),
            'rating' => $item->getRating() ? (float) $item->getRating() : null,
            'reviewsCount' => $item->getReviewsCount(),
            'createdAt' => $item->getCreatedAt()?->format('c'),
            'updatedAt' => $item->getUpdatedAt()?->format('c'),
        ]);
    }
}
