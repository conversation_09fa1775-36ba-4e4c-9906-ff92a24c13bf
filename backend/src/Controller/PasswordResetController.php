<?php

namespace App\Controller;

use App\Service\PasswordResetService;
use Symfony\Bundle\FrameworkBundle\Controller\AbstractController;
use Symfony\Component\HttpFoundation\JsonResponse;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\Routing\Annotation\Route;
use Symfony\Component\Validator\Constraints as Assert;
use Symfony\Component\Validator\Validator\ValidatorInterface;

/**
 * @Route("/api/v1")
 */
class PasswordResetController extends AbstractController
{
    public function __construct(
        private PasswordResetService $passwordResetService,
        private ValidatorInterface $validator
    ) {
    }

    /**
     * @Route("/zapomenute-heslo", name="password_forgot", methods={"POST"})
     */
    public function forgotPassword(Request $request): JsonResponse
    {
        $data = json_decode($request->getContent(), true);

        if (!$data || !isset($data['email'])) {
            return new JsonResponse(['error' => 'Email je povinný'], 400);
        }

        // Validace emailu
        $violations = $this->validator->validate($data['email'], [
            new Assert\NotBlank(),
            new Assert\Email(),
        ]);

        if (count($violations) > 0) {
            return new JsonResponse(['error' => 'Neplatný email'], 400);
        }

        $this->passwordResetService->generateResetToken($data['email']);

        return new JsonResponse([
            'message' => 'Pokud email existuje v našem systému, byl odeslán odkaz pro reset hesla.',
        ]);
    }

    /**
     * @Route("/reset-hesla", name="password_reset", methods={"POST"})
     */
    public function resetPassword(Request $request): JsonResponse
    {
        $data = json_decode($request->getContent(), true);

        if (!$data || !isset($data['token']) || !isset($data['password'])) {
            return new JsonResponse(['error' => 'Token a nové heslo jsou povinné'], 400);
        }

        // Validace hesla
        $violations = $this->validator->validate($data['password'], [
            new Assert\NotBlank(),
            new Assert\Length(min: 8, minMessage: 'Heslo musí mít alespoň 8 znaků'),
        ]);

        if (count($violations) > 0) {
            return new JsonResponse(['error' => 'Heslo musí mít alespoň 8 znaků'], 400);
        }

        $success = $this->passwordResetService->resetPassword($data['token'], $data['password']);

        if (!$success) {
            return new JsonResponse(['error' => 'Neplatný nebo expirovaný token'], 400);
        }

        return new JsonResponse([
            'message' => 'Heslo bylo úspěšně změněno',
        ]);
    }

    /**
     * @Route("/reset-hesla/verify/{token}", name="password_reset_verify", methods={"GET"})
     */
    public function verifyResetToken(string $token): JsonResponse
    {
        $isValid = $this->passwordResetService->isTokenValid($token);

        return new JsonResponse([
            'valid' => $isValid,
        ]);
    }
}
