<?php

namespace App\Controller;

use App\Entity\Item;
use App\Entity\ItemImage;
use App\Repository\ItemRepository;
use App\Repository\ItemImageRepository;
use App\Service\ImageService;
use Doctrine\ORM\EntityManagerInterface;
use Symfony\Bundle\FrameworkBundle\Controller\AbstractController;
use Symfony\Component\HttpFoundation\File\UploadedFile;
use Symfony\Component\HttpFoundation\JsonResponse;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\Routing\Annotation\Route;
use Symfony\Component\Security\Http\Attribute\IsGranted;
use Symfony\Component\Validator\Validator\ValidatorInterface;

#[Route('/api')]
class ItemImageController extends AbstractController
{
    private const MAX_IMAGES_PER_ITEM = 6;
    private const MAX_FILE_SIZE = 10 * 1024 * 1024; // 10MB
    private const ALLOWED_MIME_TYPES = ['image/jpeg', 'image/png', 'image/webp'];

    public function __construct(
        private EntityManagerInterface $entityManager,
        private ItemRepository $itemRepository,
        private ItemImageRepository $itemImageRepository,
        private ValidatorInterface $validator,
        private ImageService $imageService
    ) {
    }

    #[Route('/predmety/{id}/images', name: 'item_images_upload', methods: ['POST'])]
    #[IsGranted('ROLE_USER')]
    public function uploadImages(int $id, Request $request): JsonResponse
    {
        $item = $this->itemRepository->find($id);
        
        if (!$item) {
            return new JsonResponse(['error' => 'Item not found'], 404);
        }

        // Zkontroluj vlastnictví
        if ($item->getOwner() !== $this->getUser()) {
            return new JsonResponse(['error' => 'Access denied'], 403);
        }

        $files = $request->files->get('images', []);
        if (empty($files)) {
            return new JsonResponse(['error' => 'No files uploaded'], 400);
        }

        // Zkontroluj limit počtu obrázků
        $currentImageCount = $this->itemImageRepository->countByItem($item);
        $newImageCount = count($files);
        
        if ($currentImageCount + $newImageCount > self::MAX_IMAGES_PER_ITEM) {
            return new JsonResponse([
                'error' => 'Maximum number of images exceeded',
                'maxImages' => self::MAX_IMAGES_PER_ITEM,
                'currentCount' => $currentImageCount
            ], 400);
        }

        $uploadedImages = [];
        $errors = [];

        foreach ($files as $file) {
            if (!$file instanceof UploadedFile) {
                $errors[] = 'Invalid file format';
                continue;
            }

            // Validace souboru
            $validationResult = $this->validateFile($file);
            if ($validationResult !== true) {
                $errors[] = $validationResult;
                continue;
            }

            try {
                $itemImage = $this->processUploadedFile($file, $item);
                $uploadedImages[] = $this->serializeItemImage($itemImage);
            } catch (\Exception $e) {
                $errors[] = 'Failed to upload file: ' . $file->getClientOriginalName();
            }
        }

        return new JsonResponse([
            'uploaded' => $uploadedImages,
            'errors' => $errors,
            'totalCount' => $this->itemImageRepository->countByItem($item)
        ]);
    }

    #[Route('/predmety/{itemId}/images/{id}', name: 'item_images_delete', methods: ['DELETE'])]
    #[IsGranted('ROLE_USER')]
    public function deleteImage(int $itemId, int $id): JsonResponse
    {
        $item = $this->itemRepository->find($itemId);
        
        if (!$item) {
            return new JsonResponse(['error' => 'Item not found'], 404);
        }

        if ($item->getOwner() !== $this->getUser()) {
            return new JsonResponse(['error' => 'Access denied'], 403);
        }

        $itemImage = $this->itemImageRepository->find($id);
        
        if (!$itemImage || $itemImage->getItem() !== $item) {
            return new JsonResponse(['error' => 'Image not found'], 404);
        }

        try {
            // Smaž fyzický soubor a všechny varianty
            $this->imageService->deleteImageVariants($itemImage->getFilename(), $itemImage->getItem()->getId());

            // Smaž z databáze
            $this->entityManager->remove($itemImage);
            $this->entityManager->flush();

            return new JsonResponse(['message' => 'Image deleted successfully']);
        } catch (\Exception $e) {
            return new JsonResponse(['error' => 'Failed to delete image'], 500);
        }
    }

    #[Route('/predmety/{itemId}/images/{id}/primary', name: 'item_images_set_primary', methods: ['PUT'])]
    #[IsGranted('ROLE_USER')]
    public function setPrimaryImage(int $itemId, int $id): JsonResponse
    {
        $item = $this->itemRepository->find($itemId);
        
        if (!$item) {
            return new JsonResponse(['error' => 'Item not found'], 404);
        }

        if ($item->getOwner() !== $this->getUser()) {
            return new JsonResponse(['error' => 'Access denied'], 403);
        }

        $itemImage = $this->itemImageRepository->find($id);
        
        if (!$itemImage || $itemImage->getItem() !== $item) {
            return new JsonResponse(['error' => 'Image not found'], 404);
        }

        try {
            // Reset všechny obrázky jako ne-hlavní
            $this->itemImageRepository->resetPrimaryForItem($item);
            
            // Nastav tento obrázek jako hlavní
            $itemImage->setIsPrimary(true);
            $this->entityManager->flush();

            return new JsonResponse([
                'message' => 'Primary image set successfully',
                'image' => $this->serializeItemImage($itemImage)
            ]);
        } catch (\Exception $e) {
            return new JsonResponse(['error' => 'Failed to set primary image'], 500);
        }
    }

    #[Route('/predmety/{itemId}/images/order', name: 'item_images_update_order', methods: ['PUT'])]
    #[IsGranted('ROLE_USER')]
    public function updateOrder(int $itemId, Request $request): JsonResponse
    {
        $item = $this->itemRepository->find($itemId);
        
        if (!$item) {
            return new JsonResponse(['error' => 'Item not found'], 404);
        }

        if ($item->getOwner() !== $this->getUser()) {
            return new JsonResponse(['error' => 'Access denied'], 403);
        }

        $data = json_decode($request->getContent(), true);
        $imageIds = $data['imageIds'] ?? [];

        if (empty($imageIds)) {
            return new JsonResponse(['error' => 'No image IDs provided'], 400);
        }

        try {
            $this->itemImageRepository->updateSortOrder($imageIds);
            
            return new JsonResponse(['message' => 'Image order updated successfully']);
        } catch (\Exception $e) {
            return new JsonResponse(['error' => 'Failed to update image order'], 500);
        }
    }

    #[Route('/predmety/{itemId}/images', name: 'item_images_list', methods: ['GET'])]
    public function listImages(int $itemId): JsonResponse
    {
        $item = $this->itemRepository->find($itemId);
        
        if (!$item) {
            return new JsonResponse(['error' => 'Item not found'], 404);
        }

        $images = $this->itemImageRepository->findByItemOrderedBySortOrder($item);
        
        return new JsonResponse([
            'images' => array_map([$this, 'serializeItemImage'], $images),
            'totalCount' => count($images)
        ]);
    }

    private function validateFile(UploadedFile $file): string|true
    {
        // Zkontroluj velikost
        if ($file->getSize() > self::MAX_FILE_SIZE) {
            return 'File size exceeds maximum allowed size (10MB)';
        }

        // Zkontroluj MIME typ
        if (!in_array($file->getMimeType(), self::ALLOWED_MIME_TYPES)) {
            return 'Invalid file type. Only JPEG, PNG and WebP are allowed';
        }

        // Zkontroluj, že je soubor skutečně obrázek
        if (!getimagesize($file->getPathname())) {
            return 'File is not a valid image';
        }

        return true;
    }

    private function processUploadedFile(UploadedFile $file, Item $item): ItemImage
    {
        // Generuj unikátní název souboru
        $filename = uniqid() . '.' . $file->guessExtension();
        
        // Vytvoř složku pro obrázky produktu
        $uploadDir = $this->getParameter('kernel.project_dir') . '/public/uploads/items/' . $item->getId();
        if (!is_dir($uploadDir)) {
            mkdir($uploadDir, 0755, true);
        }

        // Přesuň soubor
        $filePath = $uploadDir . '/' . $filename;
        $file->move($uploadDir, $filename);

        // Generuj náhledy
        try {
            $this->imageService->generateThumbnails($filePath, $filename, $item->getId());
        } catch (\Exception $e) {
            // Pokud se nepodaří generovat náhledy, pokračuj bez nich
            error_log('Failed to generate thumbnails: ' . $e->getMessage());
        }

        // Vytvoř entitu
        $itemImage = new ItemImage();
        $itemImage->setFilename($filename);
        $itemImage->setOriginalFilename($file->getClientOriginalName());
        $itemImage->setFileSize($file->getSize());
        $itemImage->setMimeType($file->getMimeType());
        $itemImage->setItem($item);
        
        // Nastav pořadí
        $maxOrder = $this->itemImageRepository->findMaxSortOrderByItem($item);
        $itemImage->setSortOrder($maxOrder + 1);
        
        // Pokud je to první obrázek, nastav jako hlavní
        if ($this->itemImageRepository->countByItem($item) === 0) {
            $itemImage->setIsPrimary(true);
        }

        $this->entityManager->persist($itemImage);
        $this->entityManager->flush();

        return $itemImage;
    }



    private function serializeItemImage(ItemImage $itemImage): array
    {
        return [
            'id' => $itemImage->getId(),
            'filename' => $itemImage->getFilename(),
            'originalFilename' => $itemImage->getOriginalFilename(),
            'fileSize' => $itemImage->getFileSize(),
            'mimeType' => $itemImage->getMimeType(),
            'isPrimary' => $itemImage->isPrimary(),
            'sortOrder' => $itemImage->getSortOrder(),
            'createdAt' => $itemImage->getCreatedAt()?->format('c'),
            'urls' => $itemImage->getUrls(),
        ];
    }
}
