<?php

namespace App\Controller;

use Doctrine\DBAL\Connection;
use Symfony\Bundle\FrameworkBundle\Controller\AbstractController;
use Symfony\Component\HttpFoundation\JsonResponse;
use Symfony\Component\Routing\Annotation\Route;

#[Route('/api/v1')]
class HealthController extends AbstractController
{
    public function __construct(
        private Connection $connection
    ) {
    }

    #[Route('/health', name: 'api_health', methods: ['GET'])]
    public function health(): JsonResponse
    {
        $status = 'healthy';
        $checks = [];
        $httpCode = 200;

        // Kontrola databáze
        try {
            $this->connection->executeQuery('SELECT 1');
            $checks['database'] = [
                'status' => 'healthy',
                'message' => 'Database connection successful',
            ];
        } catch (\Exception $e) {
            $checks['database'] = [
                'status' => 'unhealthy',
                'message' => 'Database connection failed: '.$e->getMessage(),
            ];
            $status = 'unhealthy';
            $httpCode = 503;
        }

        // Kontrola aplikace
        $checks['application'] = [
            'status' => 'healthy',
            'message' => 'Application is running',
            'version' => '1.0.0',
            'environment' => $this->getParameter('kernel.environment'),
        ];

        return new JsonResponse([
            'status' => $status,
            'timestamp' => (new \DateTime())->format('c'),
            'checks' => $checks,
        ], $httpCode);
    }
}
