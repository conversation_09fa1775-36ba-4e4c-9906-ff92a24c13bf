<?php

namespace App\Controller;

use App\Entity\Address;
use App\Entity\User;
use App\Service\MailService;
use App\Service\PasswordResetService;
use Doctrine\ORM\EntityManagerInterface;
use Lexik\Bundle\JWTAuthenticationBundle\Services\JWTTokenManagerInterface;
use Symfony\Bundle\FrameworkBundle\Controller\AbstractController;
use Symfony\Component\HttpFoundation\JsonResponse;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\PasswordHasher\Hasher\UserPasswordHasherInterface;
use Symfony\Component\Routing\Annotation\Route;
use Symfony\Component\Security\Http\Attribute\IsGranted;
use Symfony\Component\Validator\Constraints as Assert;
use Symfony\Component\Validator\Validator\ValidatorInterface;

#[Route('/api/v1')]
class AuthController extends AbstractController
{
    public function __construct(
        private EntityManagerInterface $entityManager,
        private UserPasswordHasherInterface $passwordHasher,
        private ValidatorInterface $validator,
        private JWTTokenManagerInterface $jwtManager,
        private PasswordResetService $passwordResetService,
        private MailService $mailService
    ) {
    }

    #[Route('/registrace', name: 'auth_register', methods: ['POST'])]
    public function register(Request $request): JsonResponse
    {
        $data = json_decode($request->getContent(), true);

        if (!$data) {
            return new JsonResponse(['error' => 'Invalid JSON'], 400);
        }

        $email = $data['email'] ?? '';

        // Zkontroluj, zda uživatel s tímto emailem již existuje
        $existingUser = $this->entityManager->getRepository(User::class)->findOneBy(['email' => $email]);

        if ($existingUser) {
            // Pokud je uživatel již ověřený, vrať chybu
            if ($existingUser->isVerified()) {
                return new JsonResponse([
                    'error' => 'Validation failed',
                    'violations' => [
                        [
                            'field' => 'email',
                            'message' => 'Uživatel s tímto emailem již existuje.',
                        ],
                    ],
                ], 422);
            }

            // Uživatel existuje, ale není ověřený - zkontroluj čas posledního odeslání verifikačního emailu
            $now = new \DateTimeImmutable();
            $oneHourAgo = $now->modify('-1 hour');

            $lastEmailSent = $existingUser->getVerificationEmailSentAt();

            if ($lastEmailSent && $lastEmailSent > $oneHourAgo) {
                // Méně než hodina od posledního odeslání verifikačního emailu
                $waitTime = $lastEmailSent->modify('+1 hour');
                $waitMinutes = $waitTime->diff($now)->i;

                return new JsonResponse([
                    'error' => 'registration_too_soon',
                    'message' => "Musíte počkat ještě {$waitMinutes} minut před opětovnou registrací.",
                    'waitUntil' => $waitTime->format('c'),
                ], 429);
            } else {
                // Více než hodina od posledního odeslání verifikačního emailu - přesměruj na stránku pro opětovné odeslání
                return new JsonResponse([
                    'error' => 'user_not_verified',
                    'message' => 'Uživatel s tímto emailem již existuje, ale není ověřený.',
                    'redirectTo' => '/znovu-odeslat-aktivaci',
                ], 409);
            }
        }

        $user = new User();
        $user->setEmail($email);
        $user->setFirstName($data['firstName'] ?? '');
        $user->setLastName($data['lastName'] ?? '');
        $user->setPhone($data['phone'] ?? null);

        if (isset($data['password'])) {
            $hashedPassword = $this->passwordHasher->hashPassword($user, $data['password']);
            $user->setPassword($hashedPassword);
        }

        // Generuj verifikační token
        $verificationToken = bin2hex(random_bytes(32));
        $user->setVerificationToken($verificationToken);
        $user->setVerificationTokenExpiresAt(new \DateTimeImmutable('+24 hours'));
        $user->setVerified(false); // Uživatel není ověřený dokud nepotvrdí email
        $user->setVerificationEmailSentAt(new \DateTimeImmutable()); // Nastav čas odeslání verifikačního emailu

        $errors = $this->validator->validate($user);
        if (count($errors) > 0) {
            $errorMessages = [];
            foreach ($errors as $error) {
                $errorMessages[] = [
                    'field' => $error->getPropertyPath(),
                    'message' => $error->getMessage(),
                ];
            }

            return new JsonResponse(['error' => 'Validation failed', 'violations' => $errorMessages], 422);
        }

        try {
            $this->entityManager->persist($user);
            $this->entityManager->flush();

            // Pošli verifikační email
            $this->mailService->sendEmailVerification($user, $verificationToken);

            return new JsonResponse([
                'user' => [
                    'id' => $user->getId(),
                    'email' => $user->getEmail(),
                    'firstName' => $user->getFirstName(),
                    'lastName' => $user->getLastName(),
                    'isVerified' => $user->isVerified(),
                ],
                'message' => 'User registered successfully. Please check your email to verify your account.',
            ], 201);
        } catch (\Exception $e) {
            return new JsonResponse(['error' => 'Registration failed: '.$e->getMessage()], 500);
        }
    }

    #[Route('/overeni-emailu', name: 'auth_verify_email', methods: ['POST'])]
    public function verifyEmail(Request $request): JsonResponse
    {
        $data = json_decode($request->getContent(), true);
        $token = $data['token'] ?? '';

        if (!$token) {
            return new JsonResponse(['error' => 'Chybí ověřovací token.'], 400);
        }

        $user = $this->entityManager->getRepository(User::class)->findOneBy([
            'verificationToken' => $token,
        ]);

        if (!$user) {
            return new JsonResponse(['error' => 'Neplatný ověřovací token. Token již byl použit nebo neexistuje.'], 400);
        }

        if (!$user->isVerificationTokenValid()) {
            return new JsonResponse(['error' => 'Ověřovací token vypršel. Platnost tokenu je 24 hodin.'], 400);
        }

        // Ověř uživatele
        $user->setVerified(true);
        $user->setVerificationToken(null);
        $user->setVerificationTokenExpiresAt(null);

        $this->entityManager->flush();

        return new JsonResponse([
            'message' => 'Email byl úspěšně ověřen! Nyní se můžete přihlásit.',
        ], 200);
    }

    #[Route('/znovu-odeslat-aktivaci', name: 'auth_resend_activation', methods: ['POST'])]
    public function resendActivation(Request $request): JsonResponse
    {
        $data = json_decode($request->getContent(), true);
        $email = $data['email'] ?? '';

        if (!$email) {
            return new JsonResponse(['error' => 'Email je povinný'], 400);
        }

        $user = $this->entityManager->getRepository(User::class)->findOneBy(['email' => $email]);

        if (!$user) {
            return new JsonResponse(['error' => 'Uživatel s tímto emailem neexistuje.'], 404);
        }

        if ($user->isVerified()) {
            return new JsonResponse(['error' => 'Uživatel je již ověřený.'], 400);
        }

        // Zkontroluj, zda neprošla alespoň hodina od posledního odeslání verifikačního emailu
        $now = new \DateTimeImmutable();
        $oneHourAgo = $now->modify('-1 hour');

        $lastEmailSent = $user->getVerificationEmailSentAt();

        if ($lastEmailSent && $lastEmailSent > $oneHourAgo) {
            $waitTime = $lastEmailSent->modify('+1 hour');
            $waitMinutes = $waitTime->diff($now)->i;

            return new JsonResponse([
                'error' => 'Musíte počkat ještě '.$waitMinutes.' minut před opětovným odesláním aktivačního emailu.',
                'waitUntil' => $waitTime->format('c'),
            ], 429);
        }

        // Vygeneruj nový verifikační token
        $verificationToken = bin2hex(random_bytes(32));
        $user->setVerificationToken($verificationToken);
        $user->setVerificationTokenExpiresAt(new \DateTimeImmutable('+24 hours'));
        $user->setVerificationEmailSentAt(new \DateTimeImmutable()); // Nastav čas odeslání verifikačního emailu

        try {
            $this->entityManager->flush();

            // Pošli nový verifikační email
            $this->mailService->sendEmailVerification($user, $verificationToken);

            return new JsonResponse([
                'message' => 'Nový aktivační email byl odeslán. Zkontrolujte svou emailovou schránku.',
            ], 200);
        } catch (\Exception $e) {
            return new JsonResponse(['error' => 'Chyba při odesílání aktivačního emailu: '.$e->getMessage()], 500);
        }
    }

    #[Route('/prihlaseni', name: 'auth_login', methods: ['POST'])]
    public function login(): JsonResponse
    {
        return new JsonResponse(['message' => 'Login handled by JWT bundle']);
    }

    #[Route('/autentizace/refresh', name: 'auth_refresh', methods: ['POST'])]
    public function refreshToken(Request $request): JsonResponse
    {
        $data = json_decode($request->getContent(), true);

        if (!$data || !isset($data['refreshToken'])) {
            return new JsonResponse(['error' => 'Refresh token is required'], 400);
        }

        try {
            // Dekóduj refresh token (base64 encoded JSON)
            $decodedToken = base64_decode($data['refreshToken']);
            if (!$decodedToken) {
                return new JsonResponse(['error' => 'Invalid refresh token format'], 401);
            }

            $payload = json_decode($decodedToken, true);
            if (!$payload) {
                return new JsonResponse(['error' => 'Invalid refresh token format'], 401);
            }

            // Ověř, že se jedná o refresh token
            if (!isset($payload['type']) || 'refresh' !== $payload['type']) {
                return new JsonResponse(['error' => 'Invalid refresh token'], 401);
            }

            // Ověř platnost
            if (!isset($payload['exp']) || $payload['exp'] < time()) {
                return new JsonResponse(['error' => 'Refresh token expired'], 401);
            }

            // Najdi uživatele
            $userId = $payload['user_id'] ?? null;
            if (!$userId) {
                return new JsonResponse(['error' => 'Invalid refresh token'], 401);
            }

            $user = $this->entityManager->getRepository(User::class)->find($userId);
            if (!$user) {
                return new JsonResponse(['error' => 'User not found'], 401);
            }

            // Vytvoř nový access token se standardní platností (1 den)
            $newToken = $this->jwtManager->create($user);

            return new JsonResponse([
                'token' => $newToken,
                'user' => [
                    'id' => $user->getId(),
                    'email' => $user->getEmail(),
                    'firstName' => $user->getFirstName(),
                    'lastName' => $user->getLastName(),
                    'isVerified' => $user->isVerified(),
                    'roles' => $user->getRoles(),
                ],
            ]);

        } catch (\Exception) {
            return new JsonResponse(['error' => 'Invalid refresh token'], 401);
        }
    }

    #[Route('/uzivatele/profil', name: 'user_profile', methods: ['GET'])]
    #[IsGranted('ROLE_USER')]
    public function getProfile(): JsonResponse
    {
        /** @var User $user */
        $user = $this->getUser();

        return new JsonResponse([
            'id' => $user->getId(),
            'email' => $user->getEmail(),
            'firstName' => $user->getFirstName(),
            'lastName' => $user->getLastName(),
            'phone' => $user->getPhone(),
            'avatar' => $user->getAvatar(),
            'rating' => $user->getRating(),
            'reviewsCount' => $user->getReviewsCount(),
            'isVerified' => $user->isVerified(),
            'createdAt' => $user->getCreatedAt()?->format('c'),
            'address' => $user->getAddress() ? [
                'id' => $user->getAddress()->getId(),
                'street' => $user->getAddress()->getStreet(),
                'city' => $user->getAddress()->getCity(),
                'zipCode' => $user->getAddress()->getZipCode(),
            ] : null,
        ]);
    }

    #[Route('/uzivatele/profil', name: 'user_profile_update', methods: ['PUT'])]
    #[IsGranted('ROLE_USER')]
    public function updateProfile(Request $request): JsonResponse
    {
        /** @var User $user */
        $user = $this->getUser();
        $data = json_decode($request->getContent(), true);

        if (!$data) {
            return new JsonResponse(['error' => 'Invalid JSON'], 400);
        }

        // Aktualizuj základní údaje uživatele
        if (isset($data['firstName'])) {
            $user->setFirstName($data['firstName']);
        }
        if (isset($data['lastName'])) {
            $user->setLastName($data['lastName']);
        }
        if (isset($data['email'])) {
            $user->setEmail($data['email']);
        }
        if (isset($data['phone'])) {
            $user->setPhone($data['phone']);
        }

        // Aktualizuj nebo vytvoř adresu
        if (isset($data['address']) && is_array($data['address'])) {
            $addressData = $data['address'];
            $street = trim($addressData['street'] ?? '');
            $city = trim($addressData['city'] ?? '');
            $zipCode = trim($addressData['zipCode'] ?? '');

            // Zkontroluj, jestli jsou vyplněna všechna povinná pole adresy
            $hasCompleteAddress = !empty($street) && !empty($city) && !empty($zipCode);

            $address = $user->getAddress();

            if ($hasCompleteAddress) {
                // Vytvoř nebo aktualizuj adresu
                if (!$address) {
                    $address = new Address();
                    $address->setUser($user);
                    $user->setAddress($address);
                }

                $address->setStreet($street);
                $address->setCity($city);
                $address->setZipCode($zipCode);
            } else {
                // Smaž existující adresu, pokud jsou pole prázdná
                if ($address) {
                    $this->entityManager->remove($address);
                    $user->setAddress(null);
                }
            }
        }

        $user->setUpdatedAt(new \DateTimeImmutable());

        // Validace
        $errors = $this->validator->validate($user);
        if (count($errors) > 0) {
            $errorMessages = [];
            foreach ($errors as $error) {
                $errorMessages[] = [
                    'field' => $error->getPropertyPath(),
                    'message' => $error->getMessage(),
                ];
            }

            return new JsonResponse(['error' => 'Validation failed', 'violations' => $errorMessages], 422);
        }

        try {
            $this->entityManager->flush();

            // Po flush znovu načteme uživatele z databáze
            $this->entityManager->clear();
            $updatedUser = $this->entityManager->getRepository(User::class)->find($user->getId());

            return new JsonResponse([
                'id' => $updatedUser->getId(),
                'email' => $updatedUser->getEmail(),
                'firstName' => $updatedUser->getFirstName(),
                'lastName' => $updatedUser->getLastName(),
                'phone' => $updatedUser->getPhone(),
                'avatar' => $updatedUser->getAvatar(),
                'rating' => $updatedUser->getRating(),
                'reviewsCount' => $updatedUser->getReviewsCount(),
                'isVerified' => $updatedUser->isVerified(),
                'createdAt' => $updatedUser->getCreatedAt()?->format('c'),
                'address' => $updatedUser->getAddress() ? [
                    'id' => $updatedUser->getAddress()->getId(),
                    'street' => $updatedUser->getAddress()->getStreet(),
                    'city' => $updatedUser->getAddress()->getCity(),
                    'zipCode' => $updatedUser->getAddress()->getZipCode(),
                ] : null,
            ]);
        } catch (\Exception $e) {
            return new JsonResponse(['error' => 'Update failed: '.$e->getMessage()], 500);
        }
    }

    #[Route('/uzivatele/statistiky', name: 'user_stats', methods: ['GET'])]
    #[IsGranted('ROLE_USER')]
    public function getStats(): JsonResponse
    {
        /** @var User $user */
        $user = $this->getUser();

        if (!$user) {
            return new JsonResponse(['error' => 'User not found'], 404);
        }

        // Počet předmětů uživatele
        $itemsCount = count($user->getItems());

        // Počet aktivních rezervací (jako půjčovatel)
        $activeBookingsCount = 0;
        $pendingBookingsCount = 0;
        $completedBookingsCount = 0;

        foreach ($user->getBookings() as $booking) {
            switch ($booking->getStatus()) {
                case 'active':
                case 'approved':
                    $activeBookingsCount++;

                    break;
                case 'pending':
                    $pendingBookingsCount++;

                    break;
                case 'completed':
                    $completedBookingsCount++;

                    break;
            }
        }

        // Počet rezervací předmětů uživatele (jako vlastník)
        $incomingBookingsCount = 0;
        foreach ($user->getItems() as $item) {
            foreach ($item->getBookings() as $booking) {
                if (in_array($booking->getStatus(), ['pending', 'approved', 'active'])) {
                    ++$incomingBookingsCount;
                }
            }
        }

        return new JsonResponse([
            'itemsCount' => $itemsCount,
            'activeBookingsCount' => $activeBookingsCount,
            'pendingBookingsCount' => $pendingBookingsCount,
            'completedBookingsCount' => $completedBookingsCount,
            'incomingBookingsCount' => $incomingBookingsCount,
            'totalEarnings' => 0, // TODO: implementovat výpočet výdělků
        ]);
    }

    #[Route('/uzivatele/aktivity', name: 'user_activities', methods: ['GET'])]
    #[IsGranted('ROLE_USER')]
    public function getActivities(): JsonResponse
    {
        /** @var User $user */
        $user = $this->getUser();

        if (!$user) {
            return new JsonResponse(['error' => 'User not found'], 404);
        }

        $activities = [];

        // Přidej posledních 10 rezervací uživatele
        foreach ($user->getBookings() as $booking) {
            $activities[] = [
                'type' => 'booking_created',
                'message' => 'Nová rezervace pro "'.$booking->getItem()->getTitle().'"',
                'date' => $booking->getCreatedAt()->format('c'),
                'status' => $booking->getStatus(),
            ];
        }

        // Přidej rezervace předmětů uživatele
        foreach ($user->getItems() as $item) {
            foreach ($item->getBookings() as $booking) {
                $activities[] = [
                    'type' => 'booking_received',
                    'message' => 'Nová rezervace od '.$booking->getBorrower()->getFirstName().' '.substr($booking->getBorrower()->getLastName(), 0, 1).'.',
                    'date' => $booking->getCreatedAt()->format('c'),
                    'status' => $booking->getStatus(),
                ];
            }
        }

        // Seřaď podle data (nejnovější první) a vezmi pouze posledních 10
        usort($activities, function ($a, $b) {
            return strtotime($b['date']) - strtotime($a['date']);
        });

        return new JsonResponse(array_slice($activities, 0, 10));
    }

    #[Route('/zapomenute-heslo', name: 'password_forgot', methods: ['POST'])]
    public function forgotPassword(Request $request): JsonResponse
    {
        $data = json_decode($request->getContent(), true);

        if (!$data || !isset($data['email'])) {
            return new JsonResponse(['error' => 'Email je povinný'], 400);
        }

        // Validace emailu
        $violations = $this->validator->validate($data['email'], [
            new Assert\NotBlank(),
            new Assert\Email(),
        ]);

        if (count($violations) > 0) {
            return new JsonResponse(['error' => 'Neplatný email'], 400);
        }

        $this->passwordResetService->generateResetToken($data['email']);

        return new JsonResponse([
            'message' => 'Pokud email existuje v našem systému, byl odeslán odkaz pro reset hesla.',
        ]);
    }

    #[Route('/reset-hesla', name: 'password_reset', methods: ['POST'])]
    public function resetPassword(Request $request): JsonResponse
    {
        $data = json_decode($request->getContent(), true);

        if (!$data || !isset($data['token']) || !isset($data['password'])) {
            return new JsonResponse(['error' => 'Token a nové heslo jsou povinné'], 400);
        }

        // Validace hesla
        $violations = $this->validator->validate($data['password'], [
            new Assert\NotBlank(),
            new Assert\Length(min: 8, minMessage: 'Heslo musí mít alespoň 8 znaků'),
        ]);

        if (count($violations) > 0) {
            return new JsonResponse(['error' => 'Heslo musí mít alespoň 8 znaků'], 400);
        }

        $success = $this->passwordResetService->resetPassword($data['token'], $data['password']);

        if (!$success) {
            return new JsonResponse(['error' => 'Neplatný nebo expirovaný token'], 400);
        }

        return new JsonResponse([
            'message' => 'Heslo bylo úspěšně změněno',
        ]);
    }

    #[Route('/reset-hesla/verify/{token}', name: 'password_reset_verify', methods: ['GET'])]
    public function verifyResetToken(string $token): JsonResponse
    {
        $isValid = $this->passwordResetService->isTokenValid($token);

        return new JsonResponse([
            'valid' => $isValid,
        ]);
    }
}
