FROM php:8.2-cli-alpine

# Instalace systémových závislostí
RUN apk add --no-cache \
    git \
    curl \
    libzip-dev \
    zip \
    unzip \
    postgresql-dev \
    autoconf \
    gcc \
    g++ \
    make \
    linux-headers \
    && docker-php-ext-configure pgsql -with-pgsql=/usr/local/pgsql \
    && docker-php-ext-install pdo pdo_pgsql pgsql zip \
    && rm -rf /var/cache/apk/*

# Instalace Redis extension pomocí git
RUN git clone https://github.com/phpredis/phpredis.git /usr/src/php/ext/redis \
    && docker-php-ext-install redis

# Instalace Composer
COPY --from=composer:latest /usr/bin/composer /usr/bin/composer

# Nastavení pracovního adresáře
WORKDIR /var/www/html

# Kopírování .env souboru z root adres<PERSON>ře pro build
COPY ../.env ./

# Kopírování composer souborů
COPY composer.json composer.lock ./

# Vyčištění composer cache
RUN composer clear-cache

# Kopírování aplikace
COPY . .

# Instalace PHP závislostí bez post-install scriptů (spustí se při startu kontejneru)
RUN composer install --optimize-autoloader --no-scripts --verbose

# Vytvoření potřebných adresářů
RUN mkdir -p var/cache var/log config/jwt public/uploads \
    && chmod -R 777 var/ config/jwt/ public/uploads/

# Kopírování a nastavení entrypoint scriptu
COPY docker-entrypoint.sh /usr/local/bin/
RUN chmod +x /usr/local/bin/docker-entrypoint.sh

# Expose port
EXPOSE 8000

# Spuštění přes entrypoint script
ENTRYPOINT ["/usr/local/bin/docker-entrypoint.sh"]
