# 📸 Správa fotografií produktů - Implementační plán

## 🎯 Přehled projektu
Implementace kompletního systému pro nahrávání, správu a zobrazování fotografií produktů s uživatelsky přívětivým rozhraním a optimalizací obrázků.

**Klíčové požadavky:**
- Maximálně 6 fotografií na produkt
- Drag & drop nahrávání a řazení
- Označení hlavní fotografie pro seznamy
- Automatická optimalizace velikosti
- Responsive design

---

## 📋 FÁZE 1: Backend API a databáze

### [x] 1.1 Databázová struktura
- [x] Vytvořit migraci pro tabulku `item_images`
- [x] Definovat vztahy mezi `items` a `item_images`
- [x] Přidat indexy pro optimalizaci dotazů
- [x] Aktualizovat Item entitu o vztah k obrázkům

### [x] 1.2 Backend API endpointy
- [x] `POST /api/predmety/{id}/images` - Nahrání nových obr<PERSON>zků
- [x] `PUT /api/predmety/{id}/images/order` - Změna pořadí obrázků
- [x] `PUT /api/predmety/{id}/images/{imageId}/primary` - Nastavení hlavního obrázku
- [x] `DELETE /api/predmety/{id}/images/{imageId}` - Smazání obrázku
- [x] `GET /api/predmety/{id}/images` - Seznam obrázků produktu

### [x] 1.3 Validace a bezpečnost
- [x] Validace MIME typů (JPEG, PNG, WebP)
- [x] Kontrola velikosti souborů (max 10MB)
- [x] Sanitizace názvů souborů
- [x] Ověření vlastnictví produktu před úpravami
- [x] Rate limiting pro nahrávání

### [x] 1.4 Úložiště souborů
- [x] Konfigurace lokálního úložiště pro development
- [x] Struktura složek `/uploads/items/{item_id}/`
- [x] Generování unikátních názvů souborů
- [x] Cleanup při mazání produktu

---

## 📋 FÁZE 2: Frontend komponenty

### [ ] 2.1 Základní komponenty
- [ ] `ImageUploader` - Drag & drop nahrávání
- [ ] `ImageThumbnail` - Náhled s možností mazání
- [ ] `ImageGallery` - Grid pro správu obrázků
- [ ] `ImagePreview` - Modal pro full-size zobrazení

### [ ] 2.2 Integrace do formulářů
- [ ] Přidat ImageUploader do formuláře pro nový produkt
- [ ] Přidat ImageGallery do formuláře pro editaci produktu
- [ ] Aktualizovat TypeScript typy pro obrázky
- [ ] Propojit s API endpointy

### [ ] 2.3 UX/UI vylepšení
- [ ] Progress indikátory při nahrávání
- [ ] Error handling s user-friendly zprávami
- [ ] Loading states pro všechny akce
- [ ] Tooltips a nápověda pro uživatele

### [ ] 2.4 Validace na frontendu
- [ ] Real-time validace velikosti a formátu
- [ ] Upozornění při překročení limitu 6 fotografií
- [ ] Preview před nahráním
- [ ] Retry mechanismus pro neúspěšná nahrání

---

## 📋 FÁZE 3: Zobrazování a optimalizace

### [ ] 3.1 Zobrazování v aplikaci
- [ ] Hlavní fotografie v seznamech produktů
- [ ] Galerie na detailu produktu
- [ ] Placeholder pro produkty bez fotografií
- [ ] Breadcrumb navigace v galerii

### [ ] 3.2 Optimalizace obrázků
- [ ] Automatická komprese při nahrávání
- [ ] Generování náhledů (150x150, 400x400, 800x800)
- [ ] WebP formát s JPEG fallback
- [ ] Lazy loading implementace

### [ ] 3.3 Responsive design
- [ ] Mobile-first přístup
- [ ] Touch-friendly ovládání
- [ ] Swipe gestures pro galerii
- [ ] Optimalizované náhledy pro různé obrazovky

### [ ] 3.4 Performance optimalizace
- [ ] CDN ready struktura
- [ ] Caching headers pro statické soubory
- [ ] Image compression na frontendu
- [ ] Optimalizace načítání

---

## 📋 FÁZE 4: Pokročilé funkce

### [ ] 4.1 Drag & Drop funkcionalita
- [ ] Přeřazování obrázků v galerii
- [ ] Vizuální feedback při přetahování
- [ ] Označení hlavní fotografie hvězdičkou
- [ ] Automatické uložení změn pořadí

### [ ] 4.2 Pokročilé zobrazování
- [ ] Lightbox galerie s navigací
- [ ] Zoom funkcionalita
- [ ] Fullscreen režim
- [ ] Keyboard shortcuts

### [ ] 4.3 Monitoring a analytics
- [ ] Logování nahrávaných souborů
- [ ] Metriky velikosti a počtu obrázků
- [ ] Error tracking pro neúspěšná nahrání
- [ ] Performance monitoring

---

## 🔧 Technické detaily

### Databázová struktura
```sql
CREATE TABLE item_images (
    id INT PRIMARY KEY AUTO_INCREMENT,
    item_id INT NOT NULL,
    filename VARCHAR(255) NOT NULL,
    original_filename VARCHAR(255),
    file_size INT,
    mime_type VARCHAR(100),
    is_primary BOOLEAN DEFAULT FALSE,
    sort_order INT DEFAULT 0,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (item_id) REFERENCES items(id) ON DELETE CASCADE
);
```

### API Response formát
```json
{
  "id": 1,
  "filename": "uuid_medium.jpg",
  "originalFilename": "my-photo.jpg",
  "fileSize": 245760,
  "mimeType": "image/jpeg",
  "isPrimary": true,
  "sortOrder": 0,
  "urls": {
    "thumbnail": "/uploads/items/1/uuid_thumb.jpg",
    "medium": "/uploads/items/1/uuid_medium.jpg",
    "large": "/uploads/items/1/uuid_large.jpg"
  }
}
```

### Komponenty struktura
```
components/
├── images/
│   ├── ImageUploader.tsx
│   ├── ImageGallery.tsx
│   ├── ImageThumbnail.tsx
│   ├── ImagePreview.tsx
│   └── ImagePlaceholder.tsx
```

---

## 🚀 Priorita implementace

**Vysoká priorita:**
- Fáze 1: Backend API a databáze
- Fáze 2.1-2.2: Základní frontend komponenty

**Střední priorita:**
- Fáze 2.3-2.4: UX vylepšení a validace
- Fáze 3.1: Zobrazování v aplikaci

**Nízká priorita:**
- Fáze 3.2-3.4: Optimalizace a performance
- Fáze 4: Pokročilé funkce

---

## 📝 Poznámky
- Začít s jednoduchým řešením a postupně rozšiřovat
- Testovat na různých zařízeních během vývoje
- Připravit fallback řešení pro starší prohlížeče
- Dokumentovat API endpointy pro frontend tým
