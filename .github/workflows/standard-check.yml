name: Standard Check

on:
  push:
    branches: [ main, develop ]
  pull_request:
    branches: [ main, develop ]

jobs:
  frontend-checks:
    name: Frontend Code Standards
    runs-on: ubuntu-latest

    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: '18'
          cache: 'npm'
          cache-dependency-path: frontend/package-lock.json

      - name: Setup environment
        run: cp .env.example .env

      - name: Install dependencies
        working-directory: frontend
        run: npm ci

      - name: Run ESLint
        working-directory: frontend
        run: npm run lint

      - name: Run Prettier check
        working-directory: frontend
        run: npx prettier --check .

      - name: Run TypeScript check
        working-directory: frontend
        run: npm run type-check

      - name: Build test
        working-directory: frontend
        run: npm run build

  backend-checks:
    name: Backend Code Standards
    runs-on: ubuntu-latest

    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Setup PHP
        uses: shivammathur/setup-php@v2
        with:
          php-version: '8.2'
          extensions: ctype, iconv, pdo, pdo_pgsql
          tools: composer

      - name: Setup environment
        run: cp .env.example backend/.env

      - name: Get composer cache directory
        id: composer-cache
        working-directory: backend
        run: echo "dir=$(composer config cache-files-dir)" >> $GITHUB_OUTPUT

      - name: Cache composer dependencies
        uses: actions/cache@v3
        with:
          path: ${{ steps.composer-cache.outputs.dir }}
          key: ${{ runner.os }}-composer-${{ hashFiles('backend/composer.lock') }}
          restore-keys: ${{ runner.os }}-composer-

      - name: Install dependencies
        working-directory: backend
        run: composer install --prefer-dist --no-progress --no-interaction --no-scripts

      - name: Validate composer.json
        working-directory: backend
        run: composer validate --strict

      - name: Warm up Symfony cache
        working-directory: backend
        run: php bin/console cache:warmup --env=dev

      - name: Run PHP CS Fixer (dry-run)
        working-directory: backend
        run: vendor/bin/php-cs-fixer fix --dry-run --diff --verbose

      - name: Run PHPStan
        working-directory: backend
        run: vendor/bin/phpstan analyse --memory-limit=512M
