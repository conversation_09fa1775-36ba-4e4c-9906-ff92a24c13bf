# Traefik konfigurace pro produkční server
api:
  dashboard: true
  debug: false

entryPoints:
  web:
    address: ":80"
  websecure:
    address: ":443"

providers:
  docker:
    endpoint: "unix:///var/run/docker.sock"
    exposedByDefault: false
    network: pilarj-proxy_proxyNet

certificatesResolvers:
  letsencrypt:
    acme:
      email: <EMAIL>
      storage: /letsencrypt/acme.json
      httpChallenge:
        entryPoint: web

# Globální přesměrování HTTP na HTTPS pro produkci
http:
  redirections:
    entrypoint:
      to: websecure
      scheme: https

log:
  level: WARN

accessLog: {}
