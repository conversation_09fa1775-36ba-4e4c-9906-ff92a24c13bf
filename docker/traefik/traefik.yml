# Traefik konfigurace
api:
  dashboard: true
  debug: true
  insecure: true

entryPoints:
  web:
    address: ":80"
  websecure:
    address: ":443"

providers:
  docker:
    endpoint: "unix:///var/run/docker.sock"
    exposedByDefault: false
    network: pilarj-proxy_proxyNet

certificatesResolvers:
  letsencrypt:
    acme:
      email: <EMAIL>
      storage: /letsencrypt/acme.json
      httpChallenge:
        entryPoint: web

# Globální přesměrování HTTP na HTTPS (pouze pro produkci)
# http:
#   redirections:
#     entrypoint:
#       to: websecure
#       scheme: https

log:
  level: DEBUG

accessLog: {}
