services:
  # PostgreSQL databáze
  postgres:
    image: postgres:15-alpine
    container_name: pujcovna_postgres
    environment:
      POSTGRES_DB: pujcovna
      POSTGRES_USER: pujcovna
      POSTGRES_PASSWORD: pujcovna_password
    ports:
      - "15432:5432"
    volumes:
      - postgres_data:/var/lib/postgresql/data
      - ./docker/postgres/init.sql:/docker-entrypoint-initdb.d/init.sql
    networks:
      - pilarj-proxy_proxyNet

  # Redis cache
  redis:
    image: redis:7-alpine
    container_name: pujcovna_redis
    volumes:
      - redis_data:/data
    networks:
      - pilarj-proxy_proxyNet

  # MailHog pro lokální testování emailů
  mailhog:
    image: mailhog/mailhog:latest
    container_name: pujcovna_mailhog
    ports:
      - "1025:1025"  # SMTP port
      - "8025:8025"  # Web UI port
    networks:
      - pilarj-proxy_proxyNet

  # PHP/Symfony backend
  backend:
    image: pujcovna-backend:latest
    build:
      context: ./backend
      dockerfile: Dockerfile
    volumes:
      - ./backend:/var/www/html
      - ./backend/var:/var/www/html/var
      - ./backend/vendor:/var/www/html/vendor
      - ./.env:/var/www/html/.env
    env_file:
      - .env
    depends_on:
      - postgres
      - redis
    networks:
      - pilarj-proxy_proxyNet
    labels:
      - "traefik.enable=true"
      - "traefik.http.routers.backend.rule=PathPrefix(`/api/`)"
      - "traefik.http.routers.backend.entrypoints=web"
      - "traefik.http.services.backend.loadbalancer.server.port=8000"
      - "traefik.http.routers.backend.priority=10"

  # Next.js frontend
  frontend:
    image: pujcovna-frontend:latest
    build:
      context: ./frontend
      dockerfile: Dockerfile
    volumes:
      - ./frontend:/app
      - /app/node_modules
      - /app/.next
      - ./.env:/app/.env
    env_file:
      - .env
    depends_on:
      - backend
    networks:
      - pilarj-proxy_proxyNet
    labels:
      - "traefik.enable=true"
      - "traefik.http.routers.frontend.rule=Host(`${FRONTEND_HOST:-localhost}`)"
      - "traefik.http.routers.frontend.entrypoints=web"
      - "traefik.http.services.frontend.loadbalancer.server.port=3000"
      - "traefik.http.routers.frontend.priority=5"

  # Traefik reverse proxy
  traefik:
    image: traefik:v2.10
    container_name: pujcovna_traefik
    ports:
      - "18080:80"
      - "18443:443"
      - "18090:8080"  # Dashboard
    volumes:
      - /var/run/docker.sock:/var/run/docker.sock:ro
      - ./docker/traefik/traefik.yml:/etc/traefik/traefik.yml:ro
      - traefik_letsencrypt:/letsencrypt
    depends_on:
      - frontend
      - backend
    networks:
      - pilarj-proxy_proxyNet
    labels:
      - "traefik.enable=true"

volumes:
  postgres_data:
  redis_data:
  traefik_letsencrypt:

networks:
  pilarj-proxy_proxyNet:
    external: true
