import { useQuery } from 'react-query';
import { apiClient } from '@/lib/api';

export interface DashboardStats {
  itemsCount: number;
  activeBookingsCount: number;
  pendingBookingsCount: number;
  completedBookingsCount: number;
  incomingBookingsCount: number;
  totalEarnings: number;
}

export interface Activity {
  type: string;
  message: string;
  date: string;
  status: string;
}

export function useDashboardStats() {
  return useQuery<DashboardStats>(
    'dashboard-stats',
    () => apiClient.getUserStats(),
    {
      staleTime: 2 * 60 * 1000, // 2 minuty
      cacheTime: 5 * 60 * 1000, // 5 minut
      retry: 1,
      onError: (error) => {
        console.error('Chyba při načítání statistik:', error);
      },
    }
  );
}

export function useUserActivities() {
  return useQuery<Activity[]>(
    'user-activities',
    () => apiClient.getUserActivities(),
    {
      staleTime: 1 * 60 * 1000, // 1 minuta
      cacheTime: 3 * 60 * 1000, // 3 minuty
      retry: 1,
      onError: (error) => {
        console.error('Chyba při načítání aktivit:', error);
      },
    }
  );
}
