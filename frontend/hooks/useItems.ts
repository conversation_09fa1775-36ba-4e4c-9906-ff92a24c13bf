import { useQuery, useMutation, useQueryClient } from 'react-query';
import { apiClient } from '@/lib/api';
import { Item, ApiResponse } from '@/types';

interface UseItemsParams {
  page?: number;
  limit?: number;
  search?: string;
  category?: number;
  minPrice?: number;
  maxPrice?: number;
  available?: boolean;
  excludeOwn?: boolean;
  sortBy?: string;
  sortOrder?: string;
}

export function useItems(params: UseItemsParams = {}) {
  return useQuery<ApiResponse<Item[]>>(
    ['items', params],
    () => apiClient.getItems(params),
    {
      keepPreviousData: true,
      staleTime: 5 * 60 * 1000, // 5 minut
    }
  );
}

export function useItem(id: number) {
  return useQuery<Item>(['item', id], () => apiClient.getItem(id), {
    enabled: !!id,
  });
}

export function useCreateItem() {
  const queryClient = useQueryClient();

  return useMutation((data: Partial<Item>) => apiClient.createItem(data), {
    onSuccess: () => {
      // Invaliduj všechny relevantní cache
      queryClient.invalidateQueries('items');
      queryClient.invalidateQueries('my-items');
      queryClient.invalidateQueries('dashboard-stats');
      queryClient.invalidateQueries('user-activities');
    },
  });
}

export function useUpdateItem() {
  const queryClient = useQueryClient();

  return useMutation(
    ({ id, data }: { id: number; data: Partial<Item> }) =>
      apiClient.updateItem(id, data),
    {
      onSuccess: (updatedItem) => {
        queryClient.setQueryData(['item', updatedItem.id], updatedItem);
        queryClient.invalidateQueries('items');
        queryClient.invalidateQueries('my-items');
        queryClient.invalidateQueries('dashboard-stats');
      },
    }
  );
}

export function useDeleteItem() {
  const queryClient = useQueryClient();

  return useMutation((id: number) => apiClient.deleteItem(id), {
    onSuccess: () => {
      queryClient.invalidateQueries('items');
      queryClient.invalidateQueries('my-items');
      queryClient.invalidateQueries('dashboard-stats');
    },
  });
}
