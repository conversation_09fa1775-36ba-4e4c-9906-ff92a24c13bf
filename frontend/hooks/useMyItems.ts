import { useQuery } from 'react-query';
import { apiClient } from '@/lib/api';
import { Item, ApiResponse } from '@/types';

export function useMyItems(limit?: number) {
  return useQuery<ApiResponse<Item[]>>(
    ['my-items', limit],
    () => apiClient.getMyItems(),
    {
      staleTime: 2 * 60 * 1000, // 2 minuty
      cacheTime: 5 * 60 * 1000, // 5 minut
      select: (data) => {
        if (limit && data.data) {
          return {
            ...data,
            data: data.data.slice(0, limit),
          };
        }
        return data;
      },
    }
  );
}
