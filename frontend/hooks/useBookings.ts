import { useQuery, useMutation, useQueryClient } from 'react-query';
import { apiClient } from '@/lib/api';
import { Booking } from '@/types';

interface UseBookingsParams {
  status?: string;
  type?: 'outgoing' | 'incoming';
  limit?: number;
}

export function useBookings(params: UseBookingsParams = {}) {
  return useQuery<Booking[]>(
    ['bookings', params],
    () => apiClient.getBookings(params),
    {
      staleTime: 2 * 60 * 1000, // 2 minuty
      cacheTime: 5 * 60 * 1000, // 5 minut
    }
  );
}

export function useBooking(id: number) {
  return useQuery<Booking>(['booking', id], () => apiClient.getBooking(id), {
    enabled: !!id,
  });
}

export function useCreateBooking() {
  const queryClient = useQueryClient();

  return useMutation(
    (data: {
      itemId: number;
      startDate: string;
      endDate: string;
      message?: string;
    }) => apiClient.createBooking(data),
    {
      onSuccess: () => {
        queryClient.invalidateQueries('bookings');
      },
    }
  );
}

export function useApproveBooking() {
  const queryClient = useQueryClient();

  return useMutation((id: number) => apiClient.approveBooking(id), {
    onSuccess: () => {
      queryClient.invalidateQueries('bookings');
    },
  });
}

export function useRejectBooking() {
  const queryClient = useQueryClient();

  return useMutation(
    ({ id, reason }: { id: number; reason?: string }) =>
      apiClient.rejectBooking(id, reason),
    {
      onSuccess: () => {
        queryClient.invalidateQueries('bookings');
      },
    }
  );
}

export function useCompleteBooking() {
  const queryClient = useQueryClient();

  return useMutation((id: number) => apiClient.completeBooking(id), {
    onSuccess: () => {
      queryClient.invalidateQueries('bookings');
    },
  });
}

export function useCancelBooking() {
  const queryClient = useQueryClient();

  return useMutation((id: number) => apiClient.cancelBooking(id), {
    onSuccess: () => {
      queryClient.invalidateQueries('bookings');
    },
  });
}
