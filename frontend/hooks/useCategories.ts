import { useQuery } from 'react-query';
import { apiClient } from '@/lib/api';
import { Category } from '@/types';

export function useCategories() {
  return useQuery<Category[]>('categories', () => apiClient.getCategories(), {
    staleTime: 30 * 60 * 1000, // 30 minut - kategorie se mění zřídka
    cacheTime: 60 * 60 * 1000, // 1 hodina
  });
}

export function useCategory(id: number) {
  return useQuery<Category>(['category', id], () => apiClient.getCategory(id), {
    enabled: !!id,
    staleTime: 30 * 60 * 1000,
  });
}
