import { useQuery, useMutation, useQueryClient } from 'react-query';
import { apiClient } from '@/lib/api';
import { User, UpdateProfileData } from '@/types';

export function useProfile() {
  return useQuery<User>('profile', () => apiClient.getProfile(), {
    staleTime: 5 * 60 * 1000, // 5 minut
    cacheTime: 10 * 60 * 1000, // 10 minut
  });
}

export function useUpdateProfile() {
  const queryClient = useQueryClient();

  return useMutation(
    (data: UpdateProfileData) => apiClient.updateProfile(data),
    {
      onSuccess: (updatedUser) => {
        queryClient.setQueryData('profile', updatedUser);
        queryClient.setQueryData('user', updatedUser);
      },
    }
  );
}
