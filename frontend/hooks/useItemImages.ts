import { useMutation, useQuery, useQueryClient } from 'react-query';
import { api } from '@/lib/api';
import { ItemImage } from '@/types';

// Fetch images for an item
export function useItemImages(itemId: number | null) {
  return useQuery(
    ['item-images', itemId],
    async () => {
      if (!itemId) return [];
      const response = await api.get(`/predmety/${itemId}/images`);
      return response.data.images as ItemImage[];
    },
    {
      enabled: !!itemId,
      staleTime: 5 * 60 * 1000, // 5 minutes
    }
  );
}

// Upload images
export function useUploadImages() {
  const queryClient = useQueryClient();

  return useMutation(
    async ({ itemId, files }: { itemId: number; files: File[] }) => {
      const formData = new FormData();
      files.forEach((file) => {
        formData.append('images[]', file);
      });

      const response = await api.post(`/predmety/${itemId}/images`, formData, {
        headers: {
          'Content-Type': 'multipart/form-data',
        },
      });

      return response.data;
    },
    {
      onSuccess: (data, variables) => {
        // Invalidate and refetch images
        queryClient.invalidateQueries(['item-images', variables.itemId]);
        // Also invalidate item detail if it includes images
        queryClient.invalidateQueries(['item', variables.itemId]);
      },
    }
  );
}

// Delete image
export function useDeleteImage() {
  const queryClient = useQueryClient();

  return useMutation(
    async ({ itemId, imageId }: { itemId: number; imageId: number }) => {
      const response = await api.delete(`/predmety/${itemId}/images/${imageId}`);
      return response.data;
    },
    {
      onSuccess: (data, variables) => {
        queryClient.invalidateQueries(['item-images', variables.itemId]);
        queryClient.invalidateQueries(['item', variables.itemId]);
      },
    }
  );
}

// Set primary image
export function useSetPrimaryImage() {
  const queryClient = useQueryClient();

  return useMutation(
    async ({ itemId, imageId }: { itemId: number; imageId: number }) => {
      const response = await api.put(`/predmety/${itemId}/images/${imageId}/primary`);
      return response.data;
    },
    {
      onSuccess: (data, variables) => {
        queryClient.invalidateQueries(['item-images', variables.itemId]);
        queryClient.invalidateQueries(['item', variables.itemId]);
      },
    }
  );
}

// Update image order
export function useUpdateImageOrder() {
  const queryClient = useQueryClient();

  return useMutation(
    async ({ itemId, imageIds }: { itemId: number; imageIds: number[] }) => {
      const response = await api.put(`/predmety/${itemId}/images/order`, {
        imageIds,
      });
      return response.data;
    },
    {
      onSuccess: (data, variables) => {
        queryClient.invalidateQueries(['item-images', variables.itemId]);
        queryClient.invalidateQueries(['item', variables.itemId]);
      },
    }
  );
}
