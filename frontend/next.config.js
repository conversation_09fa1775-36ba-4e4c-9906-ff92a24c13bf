const { withSentryConfig } = require('@sentry/nextjs');

/** @type {import('next').NextConfig} */
const nextConfig = {
  reactStrictMode: false, // Vypni React Strict Mode pro zabránění dvojitému volání useEffect

  images: {
    domains: ['localhost', 'pujcovna.pilarj.cz'],
    unoptimized: true,
  },

  experimental: {
    optimizeCss: false,
  },

  webpack: (config, { dev, isServer }) => {
    // Oprava pro Tailwind CSS v production módu
    if (!dev && !isServer) {
      config.optimization.splitChunks.cacheGroups.styles = {
        name: 'styles',
        test: /\.(css|scss)$/,
        chunks: 'all',
        enforce: true,
      };
    }
    return config;
  },

  async rewrites() {
    // Konstanta pro backend URL - vž<PERSON> stejn<PERSON> v Docker prostředí
    const backendUrl = 'http://backend:8000';
    return [
      {
        source: '/api/:path*',
        destination: `${backendUrl}/api/:path*`,
      },
    ];
  },
};

module.exports = withSentryConfig(nextConfig, {
  org: 'pilarj',
  project: 'pujcovna-frontend',
  silent: !process.env.CI,
  widenClientFileUpload: true,
  disableLogger: true,
  automaticVercelMonitors: true,
});
