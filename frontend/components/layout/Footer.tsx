import {
  Facebook,
  Twitter,
  Instagram,
  Mail,
  Phone,
  MapPin,
} from 'lucide-react';
import LoadingLink from '@/components/ui/LoadingLink';
import { ROUTES } from '@/lib/routes';

export default function Footer() {
  return (
    <footer className="bg-gray-800 text-white">
      <div className="mx-auto max-w-7xl px-4 py-12 sm:px-6 lg:px-8">
        <div className="grid grid-cols-1 gap-8 md:grid-cols-4">
          {/* Company Info */}
          <div className="col-span-1 md:col-span-2">
            <h3 className="mb-4 text-2xl font-bold text-primary-400">
              Pujčovna
            </h3>
            <p className="mb-4 text-gray-300">
              Moderní platforma pro sdílení věcí mezi sousedy. Šetřete peníze,
              chraňte životní prostředí a posilujte komunitní vztahy.
            </p>
            <div className="flex space-x-4">
              <a href="#" className="text-gray-400 hover:text-white">
                <Facebook className="h-6 w-6" />
              </a>
              <a href="#" className="text-gray-400 hover:text-white">
                <Twitter className="h-6 w-6" />
              </a>
              <a href="#" className="text-gray-400 hover:text-white">
                <Instagram className="h-6 w-6" />
              </a>
            </div>
          </div>

          {/* Quick Links */}
          <div>
            <h4 className="mb-4 text-lg font-semibold">Rychlé odkazy</h4>
            <ul className="space-y-2">
              <li>
                <LoadingLink
                  href={ROUTES.STATIC.ABOUT}
                  className="text-gray-300 hover:text-white"
                >
                  O nás
                </LoadingLink>
              </li>
              <li>
                <LoadingLink
                  href={ROUTES.STATIC.HOW_IT_WORKS}
                  className="text-gray-300 hover:text-white"
                >
                  Jak to funguje
                </LoadingLink>
              </li>
              <li>
                <LoadingLink
                  href={ROUTES.STATIC.SAFETY}
                  className="text-gray-300 hover:text-white"
                >
                  Bezpečnost
                </LoadingLink>
              </li>
              <li>
                <LoadingLink
                  href={ROUTES.CATEGORIES}
                  className="text-gray-300 hover:text-white"
                >
                  Kategorie
                </LoadingLink>
              </li>
              <li>
                <LoadingLink
                  href={ROUTES.STATIC.BLOG}
                  className="text-gray-300 hover:text-white"
                >
                  Blog
                </LoadingLink>
              </li>
            </ul>
          </div>

          {/* Support */}
          <div>
            <h4 className="mb-4 text-lg font-semibold">Podpora</h4>
            <ul className="space-y-2">
              <li>
                <LoadingLink
                  href={ROUTES.STATIC.HELP}
                  className="text-gray-300 hover:text-white"
                >
                  Nápověda
                </LoadingLink>
              </li>
              <li>
                <LoadingLink
                  href={ROUTES.STATIC.CONTACT}
                  className="text-gray-300 hover:text-white"
                >
                  Kontakt
                </LoadingLink>
              </li>
              <li>
                <LoadingLink
                  href={ROUTES.STATIC.FAQ}
                  className="text-gray-300 hover:text-white"
                >
                  FAQ
                </LoadingLink>
              </li>
              <li>
                <LoadingLink
                  href={ROUTES.STATIC.TERMS}
                  className="text-gray-300 hover:text-white"
                >
                  Podmínky použití
                </LoadingLink>
              </li>
              <li>
                <LoadingLink
                  href={ROUTES.STATIC.PRIVACY}
                  className="text-gray-300 hover:text-white"
                >
                  Ochrana údajů
                </LoadingLink>
              </li>
            </ul>
          </div>
        </div>

        {/* Contact Info */}
        <div className="mt-8 border-t border-gray-700 pt-8">
          <div className="grid grid-cols-1 gap-4 text-sm text-gray-300 md:grid-cols-3">
            <div className="flex items-center">
              <Mail className="mr-2 h-4 w-4" />
              <span><EMAIL></span>
            </div>
            <div className="flex items-center">
              <Phone className="mr-2 h-4 w-4" />
              <span>+420 123 456 789</span>
            </div>
            <div className="flex items-center">
              <MapPin className="mr-2 h-4 w-4" />
              <span>Praha, Česká republika</span>
            </div>
          </div>
        </div>

        {/* Copyright */}
        <div className="mt-8 border-t border-gray-700 pt-8 text-center text-gray-400">
          <p>&copy; 2025 Pujčovna. Všechna práva vyhrazena.</p>
          <p className="mt-2 text-sm">Vytvořeno s ❤️ pro lepší komunity</p>
        </div>
      </div>
    </footer>
  );
}
