'use client';

import { useState } from 'react';
import { useRouter, usePathname } from 'next/navigation';
import { Menu, X, User, Plus, Bell } from 'lucide-react';
import { useAuth } from '@/hooks/useAuth';
import LoadingLink from '@/components/ui/LoadingLink';
import { ROUTES, isAuthPage } from '@/lib/routes';

export default function Header() {
  const [isMenuOpen, setIsMenuOpen] = useState(false);
  const { user, isAuthenticated, logout } = useAuth();
  const router = useRouter();
  const pathname = usePathname();

  // Skrýt odkazy na registraci/přihlášení na stránkách autentizace
  const isAuthPageCurrent = isAuthPage(pathname);

  const handleLogout = () => {
    logout();
    router.push(ROUTES.HOME);
  };

  return (
    <header className="border-b bg-white shadow-sm">
      <div className="mx-auto max-w-7xl px-4 sm:px-6 lg:px-8">
        <div className="flex h-16 items-center justify-between">
          {/* Logo */}
          <div className="flex items-center">
            <LoadingLink href={ROUTES.HOME} className="flex items-center">
              <h1 className="text-2xl font-bold text-primary-600">Pujčovna</h1>
            </LoadingLink>
          </div>

          {/* Spacer */}
          <div className="flex-1"></div>

          {/* Navigation */}
          <nav className="hidden items-center space-x-4 md:flex">
            {isAuthenticated ? (
              <>
                <LoadingLink
                  href={ROUTES.ITEMS.CREATE}
                  className="btn btn-primary flex items-center space-x-2"
                >
                  <Plus className="h-4 w-4" />
                  <span>Přidat věc</span>
                </LoadingLink>

                <LoadingLink
                  href={ROUTES.USER.NOTIFICATIONS}
                  className="p-2 text-gray-400 hover:text-gray-500"
                >
                  <Bell className="h-6 w-6" />
                </LoadingLink>

                <div className="relative">
                  <button
                    onClick={() => setIsMenuOpen(!isMenuOpen)}
                    className="flex items-center space-x-2 text-gray-700 hover:text-gray-900"
                  >
                    <div className="flex h-8 w-8 items-center justify-center rounded-full bg-primary-100">
                      <User className="h-5 w-5 text-primary-600" />
                    </div>
                    <span className="font-medium">{user?.firstName}</span>
                  </button>

                  {isMenuOpen && (
                    <div className="absolute right-0 z-50 mt-2 w-48 rounded-md bg-white py-1 shadow-lg">
                      <LoadingLink
                        href={ROUTES.USER.DASHBOARD}
                        className="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100"
                      >
                        Nástěnka
                      </LoadingLink>
                      <LoadingLink
                        href={ROUTES.USER.PROFILE}
                        className="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100"
                      >
                        Profil
                      </LoadingLink>
                      <LoadingLink
                        href={ROUTES.USER.MY_ITEMS}
                        className="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100"
                      >
                        Moje věci
                      </LoadingLink>
                      <LoadingLink
                        href={ROUTES.USER.BOOKINGS}
                        className="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100"
                      >
                        Půjčování
                      </LoadingLink>
                      <hr className="my-1" />
                      <button
                        onClick={handleLogout}
                        className="block w-full px-4 py-2 text-left text-sm text-gray-700 hover:bg-gray-100"
                      >
                        Odhlásit se
                      </button>
                    </div>
                  )}
                </div>
              </>
            ) : (
              !isAuthPageCurrent && (
                <>
                  <LoadingLink
                    href={ROUTES.AUTH.LOGIN}
                    className="btn btn-secondary"
                  >
                    Přihlásit se
                  </LoadingLink>
                  <LoadingLink
                    href={ROUTES.AUTH.REGISTER}
                    className="btn btn-primary"
                  >
                    Registrovat se
                  </LoadingLink>
                </>
              )
            )}
          </nav>

          {/* Mobile menu button */}
          <div className="md:hidden">
            <button
              onClick={() => setIsMenuOpen(!isMenuOpen)}
              className="rounded-md p-2 text-gray-400 hover:bg-gray-100 hover:text-gray-500"
            >
              {isMenuOpen ? (
                <X className="h-6 w-6" />
              ) : (
                <Menu className="h-6 w-6" />
              )}
            </button>
          </div>
        </div>
      </div>

      {/* Mobile Navigation */}
      {isMenuOpen && (
        <div className="md:hidden">
          <div className="space-y-1 border-t bg-white px-2 pb-3 pt-2 sm:px-3">
            {isAuthenticated ? (
              <>
                <LoadingLink
                  href={ROUTES.USER.DASHBOARD}
                  className="block px-3 py-2 text-base font-medium text-gray-700 hover:bg-gray-50 hover:text-gray-900"
                >
                  Nástěnka
                </LoadingLink>
                <LoadingLink
                  href={ROUTES.ITEMS.CREATE}
                  className="block px-3 py-2 text-base font-medium text-gray-700 hover:bg-gray-50 hover:text-gray-900"
                >
                  Přidat věc
                </LoadingLink>
                <LoadingLink
                  href={ROUTES.USER.MY_ITEMS}
                  className="block px-3 py-2 text-base font-medium text-gray-700 hover:bg-gray-50 hover:text-gray-900"
                >
                  Moje věci
                </LoadingLink>
                <LoadingLink
                  href={ROUTES.USER.BOOKINGS}
                  className="block px-3 py-2 text-base font-medium text-gray-700 hover:bg-gray-50 hover:text-gray-900"
                >
                  Půjčování
                </LoadingLink>
                <LoadingLink
                  href={ROUTES.USER.PROFILE}
                  className="block px-3 py-2 text-base font-medium text-gray-700 hover:bg-gray-50 hover:text-gray-900"
                >
                  Profil
                </LoadingLink>
                <button
                  onClick={handleLogout}
                  className="block w-full px-3 py-2 text-left text-base font-medium text-gray-700 hover:bg-gray-50 hover:text-gray-900"
                >
                  Odhlásit se
                </button>
              </>
            ) : (
              !isAuthPageCurrent && (
                <>
                  <LoadingLink
                    href={ROUTES.AUTH.LOGIN}
                    className="block px-3 py-2 text-base font-medium text-gray-700 hover:bg-gray-50 hover:text-gray-900"
                  >
                    Přihlásit se
                  </LoadingLink>
                  <LoadingLink
                    href={ROUTES.AUTH.REGISTER}
                    className="block px-3 py-2 text-base font-medium text-gray-700 hover:bg-gray-50 hover:text-gray-900"
                  >
                    Registrovat se
                  </LoadingLink>
                </>
              )
            )}
          </div>
        </div>
      )}
    </header>
  );
}
