'use client';

import React, { useState } from 'react';
import { Image as ImageIcon, Star } from 'lucide-react';
import { ItemImage } from '@/types';

interface ImageThumbnailProps {
  image?: ItemImage;
  images?: ItemImage[];
  alt?: string;
  className?: string;
  size?: 'sm' | 'md' | 'lg';
  showPrimaryBadge?: boolean;
  onClick?: () => void;
  placeholder?: React.ReactNode;
}

const sizeClasses = {
  sm: 'w-16 h-16',
  md: 'w-24 h-24',
  lg: 'w-32 h-32',
};

export default function ImageThumbnail({
  image,
  images,
  alt,
  className = '',
  size = 'md',
  showPrimaryBadge = false,
  onClick,
  placeholder,
}: ImageThumbnailProps) {
  const [imageError, setImageError] = useState(false);

  // Pokud je předán array obrázků, najdi hlavní nebo první
  const displayImage = image || (images && images.length > 0 
    ? images.find(img => img.isPrimary) || images[0]
    : null);

  const handleImageError = () => {
    setImageError(true);
  };

  const handleImageLoad = () => {
    setImageError(false);
  };

  // Placeholder komponenta
  const PlaceholderComponent = () => (
    <div className={`
      ${sizeClasses[size]} 
      bg-gray-100 rounded-lg flex items-center justify-center
      ${className}
    `}>
      {placeholder || (
        <div className="text-center">
          <ImageIcon className="h-6 w-6 text-gray-400 mx-auto mb-1" />
          <span className="text-xs text-gray-500">Bez obrázku</span>
        </div>
      )}
    </div>
  );

  // Pokud není obrázek nebo nastala chyba
  if (!displayImage || imageError) {
    return <PlaceholderComponent />;
  }

  return (
    <div className={`relative ${sizeClasses[size]} ${className}`}>
      <img
        src={displayImage.urls.thumbnail}
        alt={alt || displayImage.originalFilename || 'Product image'}
        className={`
          w-full h-full object-cover rounded-lg
          ${onClick ? 'cursor-pointer hover:opacity-90 transition-opacity' : ''}
        `}
        onClick={onClick}
        onError={handleImageError}
        onLoad={handleImageLoad}
      />

      {/* Primary badge */}
      {showPrimaryBadge && displayImage.isPrimary && (
        <div className="absolute -top-1 -right-1 bg-yellow-500 text-white rounded-full p-1">
          <Star className="h-3 w-3 fill-current" />
        </div>
      )}

      {/* Multiple images indicator */}
      {images && images.length > 1 && (
        <div className="absolute bottom-1 right-1 bg-black bg-opacity-60 text-white text-xs px-1.5 py-0.5 rounded">
          +{images.length - 1}
        </div>
      )}
    </div>
  );
}
