'use client';

import React, { useState } from 'react';
import { ItemImage } from '@/types';
import {
  useItemImages,
  useUploadImages,
  useDeleteImage,
  useSetPrimaryImage,
  useUpdateImageOrder,
} from '@/hooks/useItemImages';
import ImageUploader from './ImageUploader';
import ImageGallery from './ImageGallery';
import ImagePreview from './ImagePreview';

interface ItemImageManagerProps {
  itemId: number | null;
  canEdit?: boolean;
  maxImages?: number;
}

export default function ItemImageManager({
  itemId,
  canEdit = true,
  maxImages = 6,
}: ItemImageManagerProps) {
  const [previewImage, setPreviewImage] = useState<number | null>(null);
  const [isPreviewOpen, setIsPreviewOpen] = useState(false);

  // Hooks
  const { data: images = [], isLoading: isLoadingImages } = useItemImages(itemId);
  const uploadImagesMutation = useUploadImages();
  const deleteImageMutation = useDeleteImage();
  const setPrimaryImageMutation = useSetPrimaryImage();
  const updateOrderMutation = useUpdateImageOrder();

  const handleUpload = async (files: File[]) => {
    if (!itemId) throw new Error('Item ID is required');
    
    await uploadImagesMutation.mutateAsync({
      itemId,
      files,
    });
  };

  const handleDelete = async (imageId: number) => {
    if (!itemId) throw new Error('Item ID is required');
    
    await deleteImageMutation.mutateAsync({
      itemId,
      imageId,
    });
  };

  const handleSetPrimary = async (imageId: number) => {
    if (!itemId) throw new Error('Item ID is required');
    
    await setPrimaryImageMutation.mutateAsync({
      itemId,
      imageId,
    });
  };

  const handleReorder = async (imageIds: number[]) => {
    if (!itemId) throw new Error('Item ID is required');
    
    await updateOrderMutation.mutateAsync({
      itemId,
      imageIds,
    });
  };

  const handlePreview = (image: ItemImage) => {
    setPreviewImage(image.id);
    setIsPreviewOpen(true);
  };

  const handleClosePreview = () => {
    setIsPreviewOpen(false);
    setPreviewImage(null);
  };

  const isUploading = uploadImagesMutation.isLoading;
  const isAnyMutationLoading = 
    uploadImagesMutation.isLoading ||
    deleteImageMutation.isLoading ||
    setPrimaryImageMutation.isLoading ||
    updateOrderMutation.isLoading;

  if (!itemId) {
    return (
      <div className="text-center py-8 text-gray-500">
        <p>Uložte produkt pro možnost nahrání obrázků</p>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Upload section */}
      {canEdit && (
        <div>
          <h3 className="text-lg font-medium text-gray-900 mb-4">
            Nahrát obrázky
          </h3>
          <ImageUploader
            onUpload={handleUpload}
            maxFiles={maxImages}
            currentCount={images.length}
            isUploading={isUploading}
            disabled={!canEdit}
          />
        </div>
      )}

      {/* Gallery section */}
      {images.length > 0 && (
        <div>
          <ImageGallery
            images={images}
            onDelete={handleDelete}
            onSetPrimary={handleSetPrimary}
            onReorder={handleReorder}
            onPreview={handlePreview}
            isLoading={isAnyMutationLoading}
            canEdit={canEdit}
          />
        </div>
      )}

      {/* Loading state */}
      {isLoadingImages && (
        <div className="text-center py-8">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary-600 mx-auto"></div>
          <p className="mt-2 text-gray-600">Načítám obrázky...</p>
        </div>
      )}

      {/* Empty state */}
      {!isLoadingImages && images.length === 0 && !canEdit && (
        <div className="text-center py-8 text-gray-500">
          <p>Žádné obrázky nebyly nahrány</p>
        </div>
      )}

      {/* Preview modal */}
      <ImagePreview
        images={images}
        currentImageId={previewImage}
        isOpen={isPreviewOpen}
        onClose={handleClosePreview}
        onSetPrimary={canEdit ? handleSetPrimary : undefined}
        canEdit={canEdit}
      />
    </div>
  );
}
