'use client';

import React, { useState } from 'react';
import { DragDropContext, Droppable, Draggable } from '@hello-pangea/dnd';
import { Star, Trash2, Eye, GripVertical } from 'lucide-react';
import { ItemImage } from '@/types';
import toast from 'react-hot-toast';

interface ImageGalleryProps {
  images: ItemImage[];
  onDelete: (imageId: number) => Promise<void>;
  onSetPrimary: (imageId: number) => Promise<void>;
  onReorder: (imageIds: number[]) => Promise<void>;
  onPreview: (image: ItemImage) => void;
  isLoading?: boolean;
  canEdit?: boolean;
}

export default function ImageGallery({
  images,
  onDelete,
  onSetPrimary,
  onReorder,
  onPreview,
  isLoading = false,
  canEdit = true,
}: ImageGalleryProps) {
  const [deletingId, setDeletingId] = useState<number | null>(null);
  const [settingPrimaryId, setSettingPrimaryId] = useState<number | null>(null);

  const handleDelete = async (imageId: number) => {
    if (!confirm('Opravdu chcete smazat tento obrázek?')) return;

    setDeletingId(imageId);
    try {
      await onDelete(imageId);
      toast.success('Obrázek byl smazán');
    } catch (error) {
      toast.error('Chyba při mazání obrázku');
    } finally {
      setDeletingId(null);
    }
  };

  const handleSetPrimary = async (imageId: number) => {
    setSettingPrimaryId(imageId);
    try {
      await onSetPrimary(imageId);
      toast.success('Hlavní obrázek byl nastaven');
    } catch (error) {
      toast.error('Chyba při nastavování hlavního obrázku');
    } finally {
      setSettingPrimaryId(null);
    }
  };

  const handleDragEnd = async (result: any) => {
    if (!result.destination || !canEdit) return;

    const items = Array.from(images);
    const [reorderedItem] = items.splice(result.source.index, 1);
    items.splice(result.destination.index, 0, reorderedItem);

    const imageIds = items.map(item => item.id);
    
    try {
      await onReorder(imageIds);
      toast.success('Pořadí obrázků bylo aktualizováno');
    } catch (error) {
      toast.error('Chyba při změně pořadí');
    }
  };

  if (images.length === 0) {
    return (
      <div className="text-center py-8 text-gray-500">
        <div className="mx-auto h-16 w-16 bg-gray-100 rounded-full flex items-center justify-center mb-4">
          <Eye className="h-8 w-8 text-gray-400" />
        </div>
        <p>Žádné obrázky nebyly nahrány</p>
      </div>
    );
  }

  return (
    <div className="space-y-4">
      <div className="flex items-center justify-between">
        <h4 className="font-medium text-gray-900">
          Nahrané obrázky ({images.length})
        </h4>
        {canEdit && (
          <p className="text-sm text-gray-500">
            Přetáhněte pro změnu pořadí
          </p>
        )}
      </div>

      <DragDropContext onDragEnd={handleDragEnd}>
        <Droppable droppableId="images" direction="horizontal">
          {(provided) => (
            <div
              {...provided.droppableProps}
              ref={provided.innerRef}
              className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-4"
            >
              {images.map((image, index) => (
                <Draggable
                  key={image.id}
                  draggableId={image.id.toString()}
                  index={index}
                  isDragDisabled={!canEdit || isLoading}
                >
                  {(provided, snapshot) => (
                    <div
                      ref={provided.innerRef}
                      {...provided.draggableProps}
                      className={`
                        relative group rounded-lg overflow-hidden bg-white shadow-sm border
                        ${snapshot.isDragging ? 'shadow-lg scale-105' : ''}
                        ${isLoading ? 'opacity-50' : ''}
                      `}
                    >
                      {/* Drag handle */}
                      {canEdit && (
                        <div
                          {...provided.dragHandleProps}
                          className="absolute top-2 left-2 z-10 bg-black bg-opacity-50 text-white rounded p-1 opacity-0 group-hover:opacity-100 transition-opacity cursor-grab"
                        >
                          <GripVertical className="h-4 w-4" />
                        </div>
                      )}

                      {/* Primary badge */}
                      {image.isPrimary && (
                        <div className="absolute top-2 right-2 z-10 bg-yellow-500 text-white rounded-full p-1">
                          <Star className="h-4 w-4 fill-current" />
                        </div>
                      )}

                      {/* Image */}
                      <div className="aspect-square">
                        <img
                          src={image.urls.medium}
                          alt={image.originalFilename || 'Product image'}
                          className="w-full h-full object-cover cursor-pointer"
                          onClick={() => onPreview(image)}
                        />
                      </div>

                      {/* Overlay with actions */}
                      <div className="absolute inset-0 bg-black bg-opacity-0 group-hover:bg-opacity-40 transition-all flex items-center justify-center opacity-0 group-hover:opacity-100">
                        <div className="flex space-x-2">
                          {/* Preview button */}
                          <button
                            onClick={() => onPreview(image)}
                            className="bg-white text-gray-900 rounded-full p-2 hover:bg-gray-100 transition-colors"
                            title="Zobrazit"
                          >
                            <Eye className="h-4 w-4" />
                          </button>

                          {canEdit && (
                            <>
                              {/* Set primary button */}
                              {!image.isPrimary && (
                                <button
                                  onClick={() => handleSetPrimary(image.id)}
                                  disabled={settingPrimaryId === image.id}
                                  className="bg-yellow-500 text-white rounded-full p-2 hover:bg-yellow-600 transition-colors disabled:opacity-50"
                                  title="Nastavit jako hlavní"
                                >
                                  {settingPrimaryId === image.id ? (
                                    <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white"></div>
                                  ) : (
                                    <Star className="h-4 w-4" />
                                  )}
                                </button>
                              )}

                              {/* Delete button */}
                              <button
                                onClick={() => handleDelete(image.id)}
                                disabled={deletingId === image.id}
                                className="bg-red-500 text-white rounded-full p-2 hover:bg-red-600 transition-colors disabled:opacity-50"
                                title="Smazat"
                              >
                                {deletingId === image.id ? (
                                  <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white"></div>
                                ) : (
                                  <Trash2 className="h-4 w-4" />
                                )}
                              </button>
                            </>
                          )}
                        </div>
                      </div>

                      {/* Image info */}
                      <div className="p-2 bg-white">
                        <p className="text-xs text-gray-600 truncate">
                          {image.originalFilename}
                        </p>
                        {image.fileSize && (
                          <p className="text-xs text-gray-500">
                            {(image.fileSize / 1024 / 1024).toFixed(1)} MB
                          </p>
                        )}
                      </div>
                    </div>
                  )}
                </Draggable>
              ))}
              {provided.placeholder}
            </div>
          )}
        </Droppable>
      </DragDropContext>
    </div>
  );
}
