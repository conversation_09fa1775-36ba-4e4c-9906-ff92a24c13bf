'use client';

import React, { useEffect, useState } from 'react';
import { X, ChevronLeft, ChevronRight, Star, Download } from 'lucide-react';
import { ItemImage } from '@/types';

interface ImagePreviewProps {
  images: ItemImage[];
  currentImageId: number | null;
  isOpen: boolean;
  onClose: () => void;
  onSetPrimary?: (imageId: number) => Promise<void>;
  canEdit?: boolean;
}

export default function ImagePreview({
  images,
  currentImageId,
  isOpen,
  onClose,
  onSetPrimary,
  canEdit = false,
}: ImagePreviewProps) {
  const [currentIndex, setCurrentIndex] = useState(0);
  const [isLoading, setIsLoading] = useState(false);

  // Najdi index aktuálního obrázku
  useEffect(() => {
    if (currentImageId) {
      const index = images.findIndex(img => img.id === currentImageId);
      if (index !== -1) {
        setCurrentIndex(index);
      }
    }
  }, [currentImageId, images]);

  // Keyboard navigation
  useEffect(() => {
    const handleKeyDown = (e: KeyboardEvent) => {
      if (!isOpen) return;

      switch (e.key) {
        case 'Escape':
          onClose();
          break;
        case 'ArrowLeft':
          goToPrevious();
          break;
        case 'ArrowRight':
          goToNext();
          break;
      }
    };

    document.addEventListener('keydown', handleKeyDown);
    return () => document.removeEventListener('keydown', handleKeyDown);
  }, [isOpen, currentIndex]);

  // Prevent body scroll when modal is open
  useEffect(() => {
    if (isOpen) {
      document.body.style.overflow = 'hidden';
    } else {
      document.body.style.overflow = 'unset';
    }

    return () => {
      document.body.style.overflow = 'unset';
    };
  }, [isOpen]);

  const goToNext = () => {
    setCurrentIndex((prev) => (prev + 1) % images.length);
  };

  const goToPrevious = () => {
    setCurrentIndex((prev) => (prev - 1 + images.length) % images.length);
  };

  const handleSetPrimary = async () => {
    if (!onSetPrimary || !currentImage) return;

    setIsLoading(true);
    try {
      await onSetPrimary(currentImage.id);
    } catch (error) {
      console.error('Failed to set primary image:', error);
    } finally {
      setIsLoading(false);
    }
  };

  const handleDownload = () => {
    if (!currentImage) return;

    const link = document.createElement('a');
    link.href = currentImage.urls.original;
    link.download = currentImage.originalFilename || `image-${currentImage.id}`;
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
  };

  if (!isOpen || images.length === 0) return null;

  const currentImage = images[currentIndex];
  if (!currentImage) return null;

  return (
    <div className="fixed inset-0 z-50 flex items-center justify-center">
      {/* Backdrop */}
      <div
        className="absolute inset-0 bg-black bg-opacity-90"
        onClick={onClose}
      />

      {/* Modal content */}
      <div className="relative z-10 max-w-7xl max-h-full mx-4 flex flex-col">
        {/* Header */}
        <div className="flex items-center justify-between p-4 text-white">
          <div className="flex items-center space-x-4">
            <h3 className="text-lg font-medium">
              {currentImage.originalFilename || `Obrázek ${currentIndex + 1}`}
            </h3>
            {currentImage.isPrimary && (
              <div className="flex items-center space-x-1 bg-yellow-500 px-2 py-1 rounded-full text-sm">
                <Star className="h-4 w-4 fill-current" />
                <span>Hlavní</span>
              </div>
            )}
          </div>

          <div className="flex items-center space-x-2">
            {/* Image counter */}
            <span className="text-sm text-gray-300">
              {currentIndex + 1} / {images.length}
            </span>

            {/* Actions */}
            <div className="flex items-center space-x-2">
              {canEdit && onSetPrimary && !currentImage.isPrimary && (
                <button
                  onClick={handleSetPrimary}
                  disabled={isLoading}
                  className="p-2 hover:bg-white hover:bg-opacity-20 rounded-full transition-colors disabled:opacity-50"
                  title="Nastavit jako hlavní"
                >
                  {isLoading ? (
                    <div className="animate-spin rounded-full h-5 w-5 border-b-2 border-white"></div>
                  ) : (
                    <Star className="h-5 w-5" />
                  )}
                </button>
              )}

              <button
                onClick={handleDownload}
                className="p-2 hover:bg-white hover:bg-opacity-20 rounded-full transition-colors"
                title="Stáhnout"
              >
                <Download className="h-5 w-5" />
              </button>

              <button
                onClick={onClose}
                className="p-2 hover:bg-white hover:bg-opacity-20 rounded-full transition-colors"
                title="Zavřít"
              >
                <X className="h-5 w-5" />
              </button>
            </div>
          </div>
        </div>

        {/* Image container */}
        <div className="relative flex-1 flex items-center justify-center min-h-0">
          <img
            src={currentImage.urls.large}
            alt={currentImage.originalFilename || 'Product image'}
            className="max-w-full max-h-full object-contain"
          />

          {/* Navigation arrows */}
          {images.length > 1 && (
            <>
              <button
                onClick={goToPrevious}
                className="absolute left-4 top-1/2 transform -translate-y-1/2 p-3 bg-black bg-opacity-50 text-white rounded-full hover:bg-opacity-70 transition-colors"
                title="Předchozí obrázek"
              >
                <ChevronLeft className="h-6 w-6" />
              </button>

              <button
                onClick={goToNext}
                className="absolute right-4 top-1/2 transform -translate-y-1/2 p-3 bg-black bg-opacity-50 text-white rounded-full hover:bg-opacity-70 transition-colors"
                title="Další obrázek"
              >
                <ChevronRight className="h-6 w-6" />
              </button>
            </>
          )}
        </div>

        {/* Thumbnail navigation */}
        {images.length > 1 && (
          <div className="p-4">
            <div className="flex justify-center space-x-2 overflow-x-auto">
              {images.map((image, index) => (
                <button
                  key={image.id}
                  onClick={() => setCurrentIndex(index)}
                  className={`
                    flex-shrink-0 w-16 h-16 rounded-lg overflow-hidden border-2 transition-colors
                    ${index === currentIndex ? 'border-white' : 'border-transparent hover:border-gray-400'}
                  `}
                >
                  <img
                    src={image.urls.thumbnail}
                    alt={`Thumbnail ${index + 1}`}
                    className="w-full h-full object-cover"
                  />
                </button>
              ))}
            </div>
          </div>
        )}

        {/* Image info */}
        <div className="p-4 text-white text-sm">
          <div className="flex justify-between items-center">
            <div>
              {currentImage.fileSize && (
                <span>Velikost: {(currentImage.fileSize / 1024 / 1024).toFixed(1)} MB</span>
              )}
              {currentImage.mimeType && (
                <span className="ml-4">Typ: {currentImage.mimeType}</span>
              )}
            </div>
            <div className="text-gray-300">
              Nahráno: {new Date(currentImage.createdAt).toLocaleDateString('cs-CZ')}
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
