'use client';

import React, { useCallback, useState } from 'react';
import { useDropzone } from 'react-dropzone';
import { Upload, X, AlertCircle, Image as ImageIcon } from 'lucide-react';
import toast from 'react-hot-toast';

interface ImageUploaderProps {
  onUpload: (files: File[]) => Promise<void>;
  maxFiles?: number;
  currentCount?: number;
  isUploading?: boolean;
  disabled?: boolean;
}

interface FileWithPreview extends File {
  preview: string;
}

const MAX_FILE_SIZE = 10 * 1024 * 1024; // 10MB
const ACCEPTED_TYPES = {
  'image/jpeg': ['.jpg', '.jpeg'],
  'image/png': ['.png'],
  'image/webp': ['.webp'],
};

export default function ImageUploader({
  onUpload,
  maxFiles = 6,
  currentCount = 0,
  isUploading = false,
  disabled = false,
}: ImageUploaderProps) {
  const [selectedFiles, setSelectedFiles] = useState<FileWithPreview[]>([]);
  const [uploadProgress, setUploadProgress] = useState<{ [key: string]: number }>({});

  const onDrop = useCallback(
    (acceptedFiles: File[], rejectedFiles: any[]) => {
      // Zpracuj odmítnuté soubory
      rejectedFiles.forEach((file) => {
        file.errors.forEach((error: any) => {
          if (error.code === 'file-too-large') {
            toast.error(`Soubor ${file.file.name} je příliš velký (max 10MB)`);
          } else if (error.code === 'file-invalid-type') {
            toast.error(`Soubor ${file.file.name} má nepodporovaný formát`);
          } else {
            toast.error(`Chyba při nahrávání ${file.file.name}: ${error.message}`);
          }
        });
      });

      // Zkontroluj limit
      const remainingSlots = maxFiles - currentCount - selectedFiles.length;
      if (acceptedFiles.length > remainingSlots) {
        toast.error(`Můžete nahrát maximálně ${remainingSlots} dalších obrázků`);
        acceptedFiles = acceptedFiles.slice(0, remainingSlots);
      }

      // Přidej preview URL
      const filesWithPreview = acceptedFiles.map((file) =>
        Object.assign(file, {
          preview: URL.createObjectURL(file),
        })
      );

      setSelectedFiles((prev) => [...prev, ...filesWithPreview]);
    },
    [maxFiles, currentCount, selectedFiles.length]
  );

  const { getRootProps, getInputProps, isDragActive } = useDropzone({
    onDrop,
    accept: ACCEPTED_TYPES,
    maxSize: MAX_FILE_SIZE,
    disabled: disabled || isUploading,
    multiple: true,
  });

  const removeFile = (fileToRemove: FileWithPreview) => {
    setSelectedFiles((prev) => {
      const updated = prev.filter((file) => file !== fileToRemove);
      URL.revokeObjectURL(fileToRemove.preview);
      return updated;
    });
  };

  const handleUpload = async () => {
    if (selectedFiles.length === 0) return;

    try {
      await onUpload(selectedFiles);
      
      // Vyčisti preview URLs
      selectedFiles.forEach((file) => URL.revokeObjectURL(file.preview));
      setSelectedFiles([]);
      setUploadProgress({});
      
      toast.success(`Úspěšně nahráno ${selectedFiles.length} obrázků`);
    } catch (error) {
      toast.error('Chyba při nahrávání obrázků');
    }
  };

  const formatFileSize = (bytes: number) => {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  };

  const canUploadMore = currentCount + selectedFiles.length < maxFiles;

  return (
    <div className="space-y-4">
      {/* Dropzone */}
      <div
        {...getRootProps()}
        className={`
          relative border-2 border-dashed rounded-lg p-6 text-center cursor-pointer transition-colors
          ${isDragActive ? 'border-primary-500 bg-primary-50' : 'border-gray-300 hover:border-gray-400'}
          ${disabled || isUploading ? 'opacity-50 cursor-not-allowed' : ''}
          ${!canUploadMore ? 'opacity-50 cursor-not-allowed' : ''}
        `}
      >
        <input {...getInputProps()} />
        
        <div className="space-y-2">
          <Upload className="mx-auto h-12 w-12 text-gray-400" />
          
          {isDragActive ? (
            <p className="text-primary-600 font-medium">Pusťte soubory zde...</p>
          ) : (
            <div>
              <p className="text-gray-600 font-medium">
                Přetáhněte obrázky sem nebo klikněte pro výběr
              </p>
              <p className="text-sm text-gray-500 mt-1">
                Podporované formáty: JPEG, PNG, WebP (max 10MB)
              </p>
            </div>
          )}
          
          <div className="text-xs text-gray-500">
            {currentCount + selectedFiles.length} / {maxFiles} obrázků
          </div>
        </div>

        {!canUploadMore && (
          <div className="absolute inset-0 bg-gray-100 bg-opacity-75 rounded-lg flex items-center justify-center">
            <div className="text-center">
              <AlertCircle className="mx-auto h-8 w-8 text-orange-500 mb-2" />
              <p className="text-sm text-gray-600">Dosažen limit {maxFiles} obrázků</p>
            </div>
          </div>
        )}
      </div>

      {/* Preview vybraných souborů */}
      {selectedFiles.length > 0 && (
        <div className="space-y-3">
          <h4 className="font-medium text-gray-900">Vybrané soubory ({selectedFiles.length})</h4>
          
          <div className="grid grid-cols-2 md:grid-cols-3 gap-3">
            {selectedFiles.map((file, index) => (
              <div key={index} className="relative group">
                <div className="aspect-square rounded-lg overflow-hidden bg-gray-100">
                  <img
                    src={file.preview}
                    alt={file.name}
                    className="w-full h-full object-cover"
                  />
                </div>
                
                <button
                  onClick={() => removeFile(file)}
                  className="absolute -top-2 -right-2 bg-red-500 text-white rounded-full p-1 opacity-0 group-hover:opacity-100 transition-opacity"
                  disabled={isUploading}
                >
                  <X className="h-4 w-4" />
                </button>
                
                <div className="mt-1 text-xs text-gray-600 truncate">
                  {file.name}
                </div>
                <div className="text-xs text-gray-500">
                  {formatFileSize(file.size)}
                </div>
                
                {/* Progress bar */}
                {uploadProgress[file.name] !== undefined && (
                  <div className="mt-1 bg-gray-200 rounded-full h-1">
                    <div
                      className="bg-primary-600 h-1 rounded-full transition-all"
                      style={{ width: `${uploadProgress[file.name]}%` }}
                    />
                  </div>
                )}
              </div>
            ))}
          </div>

          {/* Upload button */}
          <div className="flex justify-end">
            <button
              onClick={handleUpload}
              disabled={isUploading || selectedFiles.length === 0}
              className="px-4 py-2 bg-primary-600 text-white rounded-md hover:bg-primary-700 disabled:opacity-50 disabled:cursor-not-allowed flex items-center space-x-2"
            >
              {isUploading ? (
                <>
                  <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white"></div>
                  <span>Nahrávám...</span>
                </>
              ) : (
                <>
                  <ImageIcon className="h-4 w-4" />
                  <span>Nahrát obrázky ({selectedFiles.length})</span>
                </>
              )}
            </button>
          </div>
        </div>
      )}
    </div>
  );
}
