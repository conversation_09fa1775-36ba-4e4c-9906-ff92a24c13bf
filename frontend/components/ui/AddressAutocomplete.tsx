'use client';

import { useState, useRef, useEffect, useCallback } from 'react';
import { MapPin } from 'lucide-react';

interface AddressData {
  street: string;
  city: string;
  zipCode: string;
}

interface AddressAutocompleteProps {
  onAddressSelect: (address: AddressData) => void;
  placeholder?: string;
  value?: string;
  onChange?: (value: string) => void;
  className?: string;
}

export default function AddressAutocomplete({
  onAddressSelect,
  placeholder = 'Začněte psát adresu...',
  value = '',
  onChange,
  className = '',
}: AddressAutocompleteProps) {
  const [inputValue, setInputValue] = useState(value);
  const [suggestions, setSuggestions] = useState<any[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const [showSuggestions, setShowSuggestions] = useState(false);
  const [isReady, setIsReady] = useState(true);

  // Refs pro debouncing a cancellation
  const debounceTimeoutRef = useRef<NodeJS.Timeout | null>(null);
  const abortControllerRef = useRef<AbortController | null>(null);

  const searchPlaces = async (query: string, abortSignal: AbortSignal) => {
    if (query.length < 3) {
      setSuggestions([]);
      setShowSuggestions(false);
      return;
    }

    setIsLoading(true);

    try {
      // Použijeme Nominatim API (OpenStreetMap) jako fallback
      const response = await fetch(
        `https://nominatim.openstreetmap.org/search?format=json&addressdetails=1&limit=5&countrycodes=cz&q=${encodeURIComponent(query)}`,
        { signal: abortSignal }
      );

      if (!response.ok) {
        throw new Error('Network response was not ok');
      }

      const results = await response.json();

      // Zkontroluj, jestli request nebyl zrušen
      if (abortSignal.aborted) {
        return;
      }

      setIsLoading(false);

      if (results && results.length > 0) {
        setSuggestions(results);
        setShowSuggestions(true);
      } else {
        setSuggestions([]);
        setShowSuggestions(false);
      }
    } catch (error) {
      // Ignoruj AbortError - to je očekávané chování
      if (error instanceof Error && error.name === 'AbortError') {
        return;
      }

      console.error('Chyba při vyhledávání adres:', error);
      setIsLoading(false);
      setSuggestions([]);
      setShowSuggestions(false);
    }
  };

  // Debounced search funkce
  const debouncedSearch = useCallback((query: string) => {
    // Zruš předchozí timeout
    if (debounceTimeoutRef.current) {
      clearTimeout(debounceTimeoutRef.current);
    }

    // Zruš předchozí request
    if (abortControllerRef.current) {
      abortControllerRef.current.abort();
    }

    // Pokud je query příliš krátký, vyčisti návrhy okamžitě
    if (query.length < 3) {
      setSuggestions([]);
      setShowSuggestions(false);
      setIsLoading(false);
      return;
    }

    // Nastav nový timeout pro vyhledávání
    debounceTimeoutRef.current = setTimeout(() => {
      // Vytvoř nový AbortController pro tento request
      const abortController = new AbortController();
      abortControllerRef.current = abortController;

      searchPlaces(query, abortController.signal);
    }, 500); // 500ms zpoždění
  }, []);

  // Cleanup při unmount
  useEffect(() => {
    return () => {
      if (debounceTimeoutRef.current) {
        clearTimeout(debounceTimeoutRef.current);
      }
      if (abortControllerRef.current) {
        abortControllerRef.current.abort();
      }
    };
  }, []);

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const newValue = e.target.value;
    setInputValue(newValue);
    onChange?.(newValue);

    // Použij debounced search místo okamžitého volání
    debouncedSearch(newValue);
  };

  const handleSuggestionClick = (result: any) => {
    const displayText = result.display_name || '';
    setInputValue(displayText);
    setShowSuggestions(false);
    onChange?.(displayText);

    // Parsování adresních komponent z Nominatim výsledku
    const addressData = parseNominatimResult(result);
    onAddressSelect(addressData);
  };

  const parseNominatimResult = (result: any): AddressData => {
    const address = result.address || {};

    // Sestavíme ulici z čísla popisného a názvu ulice
    let street = '';
    if (address.house_number && address.road) {
      street = `${address.road} ${address.house_number}`;
    } else if (address.road) {
      street = address.road;
    } else {
      // Fallback - použijeme první část display_name
      const parts = result.display_name?.split(',') || [];
      street = parts[0]?.trim() || '';
    }

    const city =
      address.city ||
      address.town ||
      address.village ||
      address.municipality ||
      '';
    const zipCode = address.postcode || '';

    return {
      street: street.trim(),
      city,
      zipCode,
    };
  };

  return (
    <div className="relative">
      <div className="relative">
        <MapPin className="absolute left-3 top-1/2 h-5 w-5 -translate-y-1/2 transform text-gray-400" />
        <input
          type="text"
          value={inputValue}
          onChange={handleInputChange}
          placeholder={placeholder}
          className={`w-full rounded-md border border-gray-300 py-2 pl-10 pr-3 focus:border-transparent focus:ring-2 focus:ring-primary-500 ${className}`}
          onFocus={() => {
            if (suggestions.length > 0) {
              setShowSuggestions(true);
            }
          }}
          onBlur={() => {
            // Zpoždění pro umožnění kliknutí na návrh
            setTimeout(() => setShowSuggestions(false), 200);
          }}
          disabled={!isReady}
        />
        {isLoading && (
          <div className="absolute right-3 top-1/2 -translate-y-1/2 transform">
            <div className="h-4 w-4 animate-spin rounded-full border-b-2 border-primary-500"></div>
          </div>
        )}
        {!isReady && (
          <div className="absolute right-3 top-1/2 -translate-y-1/2 transform text-xs text-gray-400">
            Načítám...
          </div>
        )}
      </div>

      {/* Návrhy */}
      {showSuggestions && suggestions.length > 0 && (
        <div className="absolute z-10 mt-1 max-h-60 w-full overflow-y-auto rounded-md border border-gray-300 bg-white shadow-lg">
          {suggestions.map((result, index) => {
            const displayText = result.display_name || '';
            const address = result.address || {};
            const locality =
              address.city || address.town || address.village || '';

            return (
              <div
                key={result.place_id || index}
                className="cursor-pointer border-b border-gray-100 px-4 py-2 last:border-b-0 hover:bg-gray-100"
                onClick={() => handleSuggestionClick(result)}
              >
                <div className="flex items-center">
                  <MapPin className="mr-2 h-4 w-4 flex-shrink-0 text-gray-400" />
                  <div>
                    <div className="text-sm font-medium text-gray-900">
                      {displayText}
                    </div>
                    {locality && (
                      <div className="text-xs text-gray-500">{locality}</div>
                    )}
                  </div>
                </div>
              </div>
            );
          })}
        </div>
      )}
    </div>
  );
}
