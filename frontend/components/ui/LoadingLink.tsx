'use client';

import Link from 'next/link';
import { useRouter } from 'next/navigation';
import { ReactNode, MouseEvent } from 'react';

interface LoadingLinkProps {
  href: string;
  children: ReactNode;
  className?: string;
  replace?: boolean;
  scroll?: boolean;
  prefetch?: boolean;
}

// Globální loading state pro navigaci
let navigationLoadingCount = 0;
const navigationLoadingCallbacks: Array<(isLoading: boolean) => void> = [];

export const subscribeToNavigationLoading = (
  callback: (isLoading: boolean) => void
) => {
  navigationLoadingCallbacks.push(callback);
  return () => {
    const index = navigationLoadingCallbacks.indexOf(callback);
    if (index > -1) {
      navigationLoadingCallbacks.splice(index, 1);
    }
  };
};

const setNavigationLoading = (isLoading: boolean) => {
  if (isLoading) {
    navigationLoadingCount++;
  } else {
    navigationLoadingCount = Math.max(0, navigationLoadingCount - 1);
  }

  const shouldShowLoading = navigationLoadingCount > 0;
  navigationLoadingCallbacks.forEach((callback) => callback(shouldShowLoading));
};

export default function LoadingLink({
  href,
  children,
  className,
  replace = false,
  scroll = true,
  prefetch = true,
  ...props
}: LoadingLinkProps) {
  const router = useRouter();

  const handleClick = (e: MouseEvent<HTMLAnchorElement>) => {
    // Pokud je to externí odkaz nebo má modifikátory, nech výchozí chování
    if (
      href.startsWith('http') ||
      href.startsWith('mailto:') ||
      href.startsWith('tel:') ||
      e.ctrlKey ||
      e.metaKey ||
      e.shiftKey ||
      e.altKey
    ) {
      return;
    }

    // Pokud je to stejná stránka, nech výchozí chování
    if (href === window.location.pathname + window.location.search) {
      return;
    }

    e.preventDefault();

    // Spustit loading
    setNavigationLoading(true);

    // Navigovat
    if (replace) {
      router.replace(href, { scroll });
    } else {
      router.push(href, { scroll });
    }

    // Ukončit loading po krátké době (Next.js navigace je rychlá)
    setTimeout(() => {
      setNavigationLoading(false);
    }, 500);
  };

  return (
    <Link
      href={href}
      className={className}
      onClick={handleClick}
      prefetch={prefetch}
      {...props}
    >
      {children}
    </Link>
  );
}
