'use client';

import { useEffect, useState, useRef } from 'react';
import { subscribeToLoading } from '@/lib/api';
import { subscribeToNavigationLoading } from './LoadingLink';

export default function LoadingProgressBar() {
  const [isApiLoading, setIsApiLoading] = useState(false);
  const [isNavigationLoading, setIsNavigationLoading] = useState(false);
  const [progress, setProgress] = useState(0);
  const intervalRef = useRef<NodeJS.Timeout | null>(null);

  const isLoading = isApiLoading || isNavigationLoading;

  useEffect(() => {
    const unsubscribeApi = subscribeToLoading((loading) => {
      setIsApiLoading(loading);
    });

    const unsubscribeNavigation = subscribeToNavigationLoading((loading) => {
      setIsNavigationLoading(loading);
    });

    return () => {
      unsubscribeApi();
      unsubscribeNavigation();
    };
  }, []);

  useEffect(() => {
    if (isLoading) {
      // Vyčistit předchozí interval
      if (intervalRef.current) {
        clearInterval(intervalRef.current);
      }

      // Spustit progress animaci
      setProgress(10);
      intervalRef.current = setInterval(() => {
        setProgress((prev) => {
          if (prev >= 90) {
            if (intervalRef.current) {
              clearInterval(intervalRef.current);
              intervalRef.current = null;
            }
            return 90; // Zastavit na 90% dokud se neskončí request
          }
          return prev + Math.random() * 15 + 5;
        });
      }, 300);
    } else {
      // Vyčistit interval
      if (intervalRef.current) {
        clearInterval(intervalRef.current);
        intervalRef.current = null;
      }

      // Dokončit progress bar
      setProgress(100);
      setTimeout(() => {
        setProgress(0);
      }, 400);
    }

    return () => {
      if (intervalRef.current) {
        clearInterval(intervalRef.current);
      }
    };
  }, [isLoading]);

  if (!isLoading && progress === 0) {
    return null;
  }

  return (
    <div className="fixed left-0 right-0 top-0 z-50">
      <div
        className="h-1 bg-blue-600 shadow-sm transition-all duration-200 ease-out"
        style={{
          width: `${progress}%`,
          opacity: isLoading || progress > 0 ? 1 : 0,
          boxShadow:
            isLoading || progress > 0
              ? '0 0 10px rgba(59, 130, 246, 0.5)'
              : 'none',
        }}
      />
    </div>
  );
}
