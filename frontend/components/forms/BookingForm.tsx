'use client';

import { useState } from 'react';
import { useForm } from 'react-hook-form';
import { Calendar, Clock, MessageSquare } from 'lucide-react';
import toast from 'react-hot-toast';
import { Item } from '@/types';
import { useCreateBooking } from '@/hooks/useBookings';
import Button from '@/components/ui/Button';

interface BookingFormProps {
  item: Item;
  onSuccess: () => void;
  onCancel: () => void;
}

interface BookingFormData {
  startDate: string;
  endDate: string;
  message: string;
}

export default function BookingForm({
  item,
  onSuccess,
  onCancel,
}: BookingFormProps) {
  const [isSubmitting, setIsSubmitting] = useState(false);
  const createBookingMutation = useCreateBooking();

  const {
    register,
    handleSubmit,
    watch,
    formState: { errors },
  } = useForm<BookingFormData>();

  const startDate = watch('startDate');
  const endDate = watch('endDate');

  // Vypočítej celkov<PERSON> cenu
  const calculateTotalPrice = () => {
    if (!startDate || !endDate) return 0;

    const start = new Date(startDate);
    const end = new Date(endDate);
    const diffTime = Math.abs(end.getTime() - start.getTime());
    const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));

    return diffDays * (item.dailyPrice || item.pricePerDay || 0);
  };

  const totalPrice = calculateTotalPrice();

  const onSubmit = async (data: BookingFormData) => {
    setIsSubmitting(true);
    try {
      await createBookingMutation.mutateAsync({
        itemId: item.id,
        startDate: new Date(data.startDate).toISOString(),
        endDate: new Date(data.endDate).toISOString(),
        message: data.message,
      });

      toast.success('Požadavek na rezervaci byl odeslán!');
      onSuccess();
    } catch (error: any) {
      toast.error(
        error.response?.data?.message || 'Chyba při vytváření rezervace'
      );
    } finally {
      setIsSubmitting(false);
    }
  };

  // Minimální datum je dnes
  const today = new Date().toISOString().split('T')[0];

  return (
    <div className="fixed inset-0 z-50 flex items-center justify-center bg-black bg-opacity-50 p-4">
      <div className="max-h-[90vh] w-full max-w-md overflow-y-auto rounded-lg bg-white shadow-xl">
        <div className="p-6">
          <h2 className="mb-4 text-2xl font-bold text-gray-900">
            Rezervace předmětu
          </h2>

          <div className="mb-4 rounded-lg bg-gray-50 p-4">
            <h3 className="font-semibold text-gray-900">{item.title}</h3>
            <p className="text-sm text-gray-600">{item.pricePerDay} Kč/den</p>
            {item.deposit && (
              <p className="text-sm text-gray-600">Záloha: {item.deposit} Kč</p>
            )}
          </div>

          <form
            method="POST"
            onSubmit={handleSubmit(onSubmit)}
            className="space-y-4"
          >
            <div>
              <label className="mb-1 block text-sm font-medium text-gray-700">
                <Calendar className="mr-1 inline h-4 w-4" />
                Datum začátku
              </label>
              <input
                {...register('startDate', {
                  required: 'Datum začátku je povinné',
                  validate: (value) => {
                    const selected = new Date(value);
                    const today = new Date();
                    today.setHours(0, 0, 0, 0);
                    return (
                      selected >= today ||
                      'Datum musí být dnes nebo v budoucnosti'
                    );
                  },
                })}
                type="date"
                min={today}
                className="input"
              />
              {errors.startDate && (
                <p className="mt-1 text-sm text-red-600">
                  {errors.startDate.message}
                </p>
              )}
            </div>

            <div>
              <label className="mb-1 block text-sm font-medium text-gray-700">
                <Calendar className="mr-1 inline h-4 w-4" />
                Datum konce
              </label>
              <input
                {...register('endDate', {
                  required: 'Datum konce je povinné',
                  validate: (value) => {
                    if (!startDate) return true;
                    const start = new Date(startDate);
                    const end = new Date(value);
                    return (
                      end > start || 'Datum konce musí být po datu začátku'
                    );
                  },
                })}
                type="date"
                min={startDate || today}
                className="input"
              />
              {errors.endDate && (
                <p className="mt-1 text-sm text-red-600">
                  {errors.endDate.message}
                </p>
              )}
            </div>

            <div>
              <label className="mb-1 block text-sm font-medium text-gray-700">
                <MessageSquare className="mr-1 inline h-4 w-4" />
                Zpráva pro vlastníka (volitelné)
              </label>
              <textarea
                {...register('message')}
                rows={3}
                className="input"
                placeholder="Napište zprávu vlastníkovi..."
              />
            </div>

            {totalPrice > 0 && (
              <div className="rounded-lg bg-blue-50 p-4">
                <div className="flex items-center justify-between">
                  <span className="font-medium text-gray-900">
                    Celková cena:
                  </span>
                  <span className="text-xl font-bold text-blue-600">
                    {totalPrice} Kč
                  </span>
                </div>
                {item.deposit && (
                  <div className="mt-1 flex items-center justify-between">
                    <span className="text-sm text-gray-600">Záloha:</span>
                    <span className="text-sm text-gray-600">
                      {item.deposit} Kč
                    </span>
                  </div>
                )}
              </div>
            )}

            <div className="flex space-x-3 pt-4">
              <Button
                type="button"
                variant="outline"
                onClick={onCancel}
                className="flex-1"
              >
                Zrušit
              </Button>
              <Button type="submit" isLoading={isSubmitting} className="flex-1">
                {isSubmitting ? 'Odesílám...' : 'Odeslat požadavek'}
              </Button>
            </div>
          </form>
        </div>
      </div>
    </div>
  );
}
