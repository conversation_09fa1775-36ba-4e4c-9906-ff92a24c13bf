import Link from 'next/link';
import { MapPin, Star, User } from 'lucide-react';
import { Item, User as UserType } from '@/types';
import { ROUTES } from '@/lib/routes';
import { getItemPrice } from '@/lib/price-utils';

interface ItemCardProps {
  item: Item;
  currentUser?: UserType | null;
}

export default function ItemCard({ item, currentUser }: ItemCardProps) {
  const getConditionColor = (condition: string) => {
    switch (condition) {
      case 'excellent':
        return 'bg-green-100 text-green-800';
      case 'good':
        return 'bg-blue-100 text-blue-800';
      case 'fair':
        return 'bg-yellow-100 text-yellow-800';
      case 'poor':
        return 'bg-red-100 text-red-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  const getConditionText = (condition: string) => {
    switch (condition) {
      case 'excellent':
        return 'Výborný';
      case 'good':
        return 'Dobrý';
      case 'fair':
        return '<PERSON>r<PERSON>m<PERSON><PERSON><PERSON>';
      case 'poor':
        return 'Špatný';
      default:
        return condition;
    }
  };

  const isMyItem = currentUser && item.owner?.id === currentUser.id;

  return (
    <Link href={ROUTES.ITEMS.DETAIL(item.id)}>
      <div className="cursor-pointer overflow-hidden rounded-lg bg-white shadow-sm transition-shadow duration-200 hover:shadow-md">
        {/* Image placeholder */}
        <div className="aspect-w-16 aspect-h-9 relative bg-gray-200">
          <div className="flex h-48 w-full items-center justify-center bg-gradient-to-br from-gray-100 to-gray-200">
            <span className="text-4xl font-bold text-gray-400">
              {item.title.charAt(0).toUpperCase()}
            </span>
          </div>
          {/* Štítek pro vlastní předmět */}
          {isMyItem && (
            <div className="absolute right-2 top-2">
              <span className="inline-flex rounded-full bg-blue-100 px-2 py-1 text-xs font-semibold text-blue-800">
                Můj předmět
              </span>
            </div>
          )}
        </div>

        <div className="p-4">
          {/* Title and Category */}
          <div className="mb-2">
            <h3 className="truncate text-lg font-semibold text-gray-900">
              {item.title}
            </h3>
            {item.category && (
              <p className="text-sm text-gray-500">{item.category.name}</p>
            )}
          </div>

          {/* Description */}
          <p className="mb-3 line-clamp-2 text-sm text-gray-600">
            {item.description}
          </p>

          {/* Condition */}
          {item.condition && (
            <div className="mb-3">
              <span
                className={`inline-flex rounded-full px-2 py-1 text-xs font-medium ${getConditionColor(item.condition)}`}
              >
                {getConditionText(item.condition)}
              </span>
            </div>
          )}

          {/* Price */}
          <div className="mb-3 flex items-center justify-between">
            <div className="flex items-center space-x-1">
              <span className="text-lg font-bold text-gray-900">
                {getItemPrice(item) || 'N/A'}
              </span>
              <span className="text-sm text-gray-500">Kč/den</span>
            </div>

            {(item.averageRating || item.rating) && (
              <div className="flex items-center space-x-1">
                <Star className="h-4 w-4 fill-current text-yellow-400" />
                <span className="text-sm text-gray-600">
                  {(item.averageRating || item.rating || 0).toFixed(1)}
                </span>
              </div>
            )}
          </div>

          {/* Owner and Location */}
          <div className="flex items-center justify-between text-sm text-gray-500">
            <div className="flex items-center space-x-1">
              <User className="h-4 w-4" />
              <span>
                {item.owner?.firstName} {item.owner?.lastName}
              </span>
            </div>

            {item.owner?.address?.city && (
              <div className="flex items-center space-x-1">
                <MapPin className="h-4 w-4" />
                <span>{item.owner.address.city}</span>
              </div>
            )}
          </div>

          {/* Availability */}
          <div className="mt-3 border-t border-gray-100 pt-3">
            <div className="flex items-center justify-between">
              <span
                className={`inline-flex rounded-full px-2 py-1 text-xs font-medium ${
                  item.isAvailable
                    ? 'bg-green-100 text-green-800'
                    : 'bg-red-100 text-red-800'
                }`}
              >
                {item.isAvailable ? 'Dostupný' : 'Nedostupný'}
              </span>

              {item.distance && (
                <span className="text-xs text-gray-500">
                  {item.distance.toFixed(1)} km
                </span>
              )}
            </div>
          </div>
        </div>
      </div>
    </Link>
  );
}
