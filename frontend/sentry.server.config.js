import * as Sentry from '@sentry/nextjs';

Sentry.init({
  dsn: process.env.NEXT_PUBLIC_SENTRY_DSN,
  environment:
    process.env.NEXT_PUBLIC_SENTRY_ENVIRONMENT ||
    process.env.NODE_ENV ||
    'development',
  tracesSampleRate: parseFloat(
    process.env.NEXT_PUBLIC_SENTRY_TRACES_SAMPLE_RATE || '1.0'
  ),

  // Debug mode pro development
  debug: process.env.NODE_ENV === 'development',

  // <PERSON><PERSON><PERSON><PERSON> unhandled exceptions
  beforeSend(event) {
    console.log(
      '🚨 Sentry: Posílám chybu do Sentry:',
      event.exception?.values?.[0]?.value
    );
    return event;
  },
});

// Zachytávání unhandled rejections
process.on('unhandledRejection', (reason, promise) => {
  console.log('🚨 Unhandled Rejection at:', promise, 'reason:', reason);
  Sentry.captureException(reason);
});

// Zachytávání uncaught exceptions
process.on('uncaughtException', (error) => {
  console.log('🚨 Uncaught Exception:', error);
  Sentry.captureException(error);
});
