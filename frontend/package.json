{"name": "<PERSON>j<PERSON>vna-frontend", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev -H 0.0.0.0", "build": "next build", "build:static": "BUILD_MODE=static next build", "start": "next start", "lint": "next lint", "lint:fix": "next lint --fix", "type-check": "tsc --noEmit", "format": "prettier --write .", "format:check": "prettier --check ."}, "dependencies": {"@hello-pangea/dnd": "^16.2.0", "@sentry/nextjs": "^9.32.0", "@types/js-cookie": "^3.0.6", "@types/node": "^20", "@types/react": "^18", "@types/react-dom": "^18", "autoprefixer": "^10.0.1", "axios": "^1.6.0", "clsx": "^2.0.0", "js-cookie": "^3.0.5", "lucide-react": "^0.294.0", "next": "14.0.4", "postcss": "^8", "react": "^18", "react-dom": "^18", "react-dropzone": "^14.2.3", "react-hook-form": "^7.48.0", "react-hot-toast": "^2.4.1", "react-query": "^3.39.0", "tailwind-merge": "^2.0.0", "tailwindcss": "^3.3.0", "typescript": "^5"}, "devDependencies": {"@typescript-eslint/eslint-plugin": "^6.0.0", "@typescript-eslint/parser": "^6.0.0", "eslint": "^8", "eslint-config-next": "14.0.4", "prettier": "^3.0.0", "prettier-plugin-tailwindcss": "^0.5.0"}}