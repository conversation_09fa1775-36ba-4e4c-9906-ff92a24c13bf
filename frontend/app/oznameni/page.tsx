'use client';

import { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import { Bell, Check, X, Clock, User, Package } from 'lucide-react';
import { useAuth } from '@/hooks/useAuth';
import { ROUTES } from '@/lib/routes';
import Layout from '@/components/layout/Layout';

interface Notification {
  id: number;
  type:
    | 'booking_request'
    | 'booking_approved'
    | 'booking_rejected'
    | 'booking_completed'
    | 'message';
  title: string;
  message: string;
  isRead: boolean;
  createdAt: string;
  relatedId?: number;
}

export default function OznameniPage() {
  const router = useRouter();
  const { isAuthenticated, isLoadingUser } = useAuth();
  const [notifications, setNotifications] = useState<Notification[]>([]);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    // Počkáme na dokončení načítání uživatele
    if (!isLoadingUser && !isAuthenticated) {
      router.push(ROUTES.AUTH.LOGIN);
      return;
    }

    // Mock data - v reálné aplikaci by se načítala z API
    const mockNotifications: Notification[] = [
      {
        id: 1,
        type: 'booking_request',
        title: 'Nová žádost o půjčení',
        message: 'Jan Novák požádal o půjčení vaší vrtačky Bosch Professional',
        isRead: false,
        createdAt: new Date(Date.now() - 2 * 60 * 60 * 1000).toISOString(),
        relatedId: 123,
      },
      {
        id: 2,
        type: 'booking_approved',
        title: 'Žádost schválena',
        message: 'Vaše žádost o půjčení sekačky byla schválena',
        isRead: false,
        createdAt: new Date(Date.now() - 5 * 60 * 60 * 1000).toISOString(),
        relatedId: 124,
      },
      {
        id: 3,
        type: 'booking_completed',
        title: 'Půjčení dokončeno',
        message: 'Půjčení brusky bylo úspěšně dokončeno',
        isRead: true,
        createdAt: new Date(Date.now() - 24 * 60 * 60 * 1000).toISOString(),
        relatedId: 125,
      },
      {
        id: 4,
        type: 'message',
        title: 'Nová zpráva',
        message: 'Máte novou zprávu od uživatele Marie Svobodová',
        isRead: true,
        createdAt: new Date(Date.now() - 48 * 60 * 60 * 1000).toISOString(),
      },
    ];

    if (isAuthenticated) {
      setNotifications(mockNotifications);
      setLoading(false);
    }
  }, [isAuthenticated, isLoadingUser, router]);

  const markAsRead = (notificationId: number) => {
    setNotifications((prev) =>
      prev.map((notification) =>
        notification.id === notificationId
          ? { ...notification, isRead: true }
          : notification
      )
    );
  };

  const markAllAsRead = () => {
    setNotifications((prev) =>
      prev.map((notification) => ({ ...notification, isRead: true }))
    );
  };

  const deleteNotification = (notificationId: number) => {
    setNotifications((prev) =>
      prev.filter((notification) => notification.id !== notificationId)
    );
  };

  const getNotificationIcon = (type: string) => {
    switch (type) {
      case 'booking_request':
        return <Package className="h-5 w-5 text-blue-500" />;
      case 'booking_approved':
        return <Check className="h-5 w-5 text-green-500" />;
      case 'booking_rejected':
        return <X className="h-5 w-5 text-red-500" />;
      case 'booking_completed':
        return <Clock className="h-5 w-5 text-purple-500" />;
      case 'message':
        return <User className="h-5 w-5 text-orange-500" />;
      default:
        return <Bell className="h-5 w-5 text-gray-500" />;
    }
  };

  const getTimeAgo = (dateString: string) => {
    const now = new Date();
    const date = new Date(dateString);
    const diffInHours = Math.floor(
      (now.getTime() - date.getTime()) / (1000 * 60 * 60)
    );

    if (diffInHours < 1) {
      return 'Před chvílí';
    } else if (diffInHours < 24) {
      return `Před ${diffInHours} h`;
    } else {
      const diffInDays = Math.floor(diffInHours / 24);
      return `Před ${diffInDays} dny`;
    }
  };

  // Zobrazíme loading během ověřování uživatele
  if (isLoadingUser) {
    return (
      <Layout>
        <div className="flex min-h-screen items-center justify-center bg-gray-50">
          <div className="text-center">
            <div className="mx-auto h-32 w-32 animate-spin rounded-full border-b-2 border-primary-600"></div>
            <p className="mt-4 text-gray-600">Načítám...</p>
          </div>
        </div>
      </Layout>
    );
  }

  if (!isAuthenticated) {
    return null;
  }

  if (loading) {
    return (
      <Layout>
        <div className="flex min-h-screen items-center justify-center bg-gray-50">
          <div className="text-center">
            <div className="mx-auto h-32 w-32 animate-spin rounded-full border-b-2 border-primary-600"></div>
            <p className="mt-4 text-gray-600">Načítám oznámení...</p>
          </div>
        </div>
      </Layout>
    );
  }

  const unreadCount = notifications.filter((n) => !n.isRead).length;

  return (
    <Layout>
      <div className="min-h-screen bg-gray-50">
        <div className="mx-auto max-w-4xl px-4 py-8 sm:px-6 lg:px-8">
          <div className="mb-8 flex items-center justify-between">
            <div>
              <h1 className="text-3xl font-bold text-gray-900">Oznámení</h1>
              {unreadCount > 0 && (
                <p className="mt-1 text-gray-600">
                  Máte {unreadCount} nepřečtených oznámení
                </p>
              )}
            </div>

            {unreadCount > 0 && (
              <button onClick={markAllAsRead} className="btn btn-secondary">
                Označit vše jako přečtené
              </button>
            )}
          </div>

          {notifications.length > 0 ? (
            <div className="divide-y divide-gray-200 rounded-lg bg-white shadow-sm">
              {notifications.map((notification) => (
                <div
                  key={notification.id}
                  className={`p-6 transition-colors hover:bg-gray-50 ${
                    !notification.isRead
                      ? 'border-l-4 border-l-blue-500 bg-blue-50'
                      : ''
                  }`}
                >
                  <div className="flex items-start justify-between">
                    <div className="flex flex-1 items-start space-x-4">
                      <div className="mt-1 flex-shrink-0">
                        {getNotificationIcon(notification.type)}
                      </div>

                      <div className="min-w-0 flex-1">
                        <div className="flex items-center justify-between">
                          <h3
                            className={`text-sm font-medium ${
                              !notification.isRead
                                ? 'text-gray-900'
                                : 'text-gray-700'
                            }`}
                          >
                            {notification.title}
                          </h3>
                          <span className="ml-2 text-xs text-gray-500">
                            {getTimeAgo(notification.createdAt)}
                          </span>
                        </div>

                        <p
                          className={`mt-1 text-sm ${
                            !notification.isRead
                              ? 'text-gray-800'
                              : 'text-gray-600'
                          }`}
                        >
                          {notification.message}
                        </p>

                        {notification.relatedId && (
                          <div className="mt-3">
                            <button
                              onClick={() => router.push(ROUTES.USER.BOOKINGS)}
                              className="text-sm font-medium text-primary-600 hover:text-primary-700"
                            >
                              Zobrazit detail →
                            </button>
                          </div>
                        )}
                      </div>
                    </div>

                    <div className="ml-4 flex items-center space-x-2">
                      {!notification.isRead && (
                        <button
                          onClick={() => markAsRead(notification.id)}
                          className="text-blue-600 hover:text-blue-700"
                          title="Označit jako přečtené"
                        >
                          <Check className="h-4 w-4" />
                        </button>
                      )}

                      <button
                        onClick={() => deleteNotification(notification.id)}
                        className="text-red-600 hover:text-red-700"
                        title="Smazat oznámení"
                      >
                        <X className="h-4 w-4" />
                      </button>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          ) : (
            <div className="rounded-lg bg-white p-12 text-center shadow-sm">
              <Bell className="mx-auto mb-4 h-16 w-16 text-gray-400" />
              <h3 className="mb-2 text-lg font-medium text-gray-900">
                Žádná oznámení
              </h3>
              <p className="text-gray-600">
                Zatím nemáte žádná oznámení. Budeme vás informovat o nových
                aktivitách.
              </p>
            </div>
          )}
        </div>
      </div>
    </Layout>
  );
}
