'use client';

import Layout from '@/components/layout/Layout';
import LoadingLink from '@/components/ui/LoadingLink';

function BackButton() {
  const handleBack = () => {
    if (typeof window !== 'undefined') {
      window.history.back();
    }
  };

  return (
    <button
      onClick={handleBack}
      className="inline-flex w-full items-center justify-center rounded-md border border-gray-300 bg-white px-4 py-2 text-sm font-medium text-gray-700 transition-colors hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2"
    >
      <svg
        className="mr-2 h-4 w-4"
        fill="none"
        stroke="currentColor"
        viewBox="0 0 24 24"
      >
        <path
          strokeLinecap="round"
          strokeLinejoin="round"
          strokeWidth="2"
          d="M10 19l-7-7m0 0l7-7m-7 7h18"
        ></path>
      </svg>
      <span><PERSON><PERSON><PERSON><PERSON> na předchozí stránku</span>
    </button>
  );
}

export default function NotFound() {
  return (
    <Layout>
      <div className="flex min-h-screen items-center justify-center bg-gray-50 px-4 sm:px-6 lg:px-8">
        <div className="w-full max-w-md text-center">
          {/* 404 Illustration */}
          <div className="mb-8">
            <div className="mx-auto mb-6 flex h-32 w-32 items-center justify-center rounded-full bg-blue-100">
              <svg
                className="h-16 w-16 text-blue-600"
                fill="none"
                stroke="currentColor"
                viewBox="0 0 24 24"
              >
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth="2"
                  d="M8.228 9c.549-1.165 2.03-2 3.772-2 2.21 0 4 1.343 4 3 0 1.4-1.278 2.575-3.006 2.907-.542.104-.994.54-.994 1.093m0 3h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"
                ></path>
              </svg>
            </div>
            <h1 className="mb-2 text-6xl font-bold text-gray-900">404</h1>
            <h2 className="mb-4 text-2xl font-semibold text-gray-700">
              Stránka nebyla nalezena
            </h2>
            <p className="mb-8 text-gray-600">
              Omlouváme se, ale stránka, kterou hledáte, neexistuje nebo byla
              přesunuta.
            </p>
          </div>

          {/* Action Buttons */}
          <div className="space-y-4">
            <LoadingLink
              href="/"
              className="inline-flex w-full items-center justify-center rounded-md border border-transparent bg-blue-600 px-4 py-2 text-sm font-medium text-white transition-colors hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2"
            >
              <svg
                className="mr-2 h-4 w-4"
                fill="none"
                stroke="currentColor"
                viewBox="0 0 24 24"
              >
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth="2"
                  d="M3 12l2-2m0 0l7-7 7 7M5 10v10a1 1 0 001 1h3m10-11l2 2m-2-2v10a1 1 0 01-1 1h-3m-6 0a1 1 0 001-1v-4a1 1 0 011-1h2a1 1 0 011 1v4a1 1 0 001 1m-6 0h6"
                ></path>
              </svg>
              <span>Zpět na hlavní stránku</span>
            </LoadingLink>

            <LoadingLink
              href="/predmety"
              className="inline-flex w-full items-center justify-center rounded-md border border-transparent bg-gray-200 px-4 py-2 text-sm font-medium text-gray-900 transition-colors hover:bg-gray-300 focus:outline-none focus:ring-2 focus:ring-gray-500 focus:ring-offset-2"
            >
              <svg
                className="mr-2 h-4 w-4"
                fill="none"
                stroke="currentColor"
                viewBox="0 0 24 24"
              >
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth="2"
                  d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"
                ></path>
              </svg>
              <span>Prohlédnout předměty</span>
            </LoadingLink>

            <BackButton />
          </div>

          {/* Helpful Links */}
          <div className="mt-12 border-t border-gray-200 pt-8">
            <h3 className="mb-4 text-lg font-medium text-gray-900">
              Možná vás bude zajímat
            </h3>
            <div className="grid grid-cols-2 gap-4 text-sm">
              <LoadingLink
                href="/jak-to-funguje"
                className="text-blue-600 hover:text-blue-700 hover:underline"
              >
                Jak to funguje
              </LoadingLink>
              <LoadingLink
                href="/napoveda"
                className="text-blue-600 hover:text-blue-700 hover:underline"
              >
                Nápověda
              </LoadingLink>
              <LoadingLink
                href="/kontakt"
                className="text-blue-600 hover:text-blue-700 hover:underline"
              >
                Kontakt
              </LoadingLink>
              <LoadingLink
                href="/faq"
                className="text-blue-600 hover:text-blue-700 hover:underline"
              >
                FAQ
              </LoadingLink>
            </div>
          </div>

          {/* Additional Help */}
          <div className="mt-8 rounded-lg bg-blue-50 p-4">
            <p className="text-sm text-blue-800">
              <strong>Tip:</strong> Zkontrolujte správnost URL adresy nebo
              použijte vyhledávání pro nalezení toho, co hledáte.
            </p>
          </div>
        </div>
      </div>
    </Layout>
  );
}
