'use client';

import { useState } from 'react';
import Link from 'next/link';
import { Mail, ArrowLeft, CheckCircle } from 'lucide-react';
import Layout from '@/components/layout/Layout';
import Button from '@/components/ui/Button';
import { ROUTES } from '@/lib/routes';

export default function ForgotPasswordPage() {
  const [email, setEmail] = useState('');
  const [isLoading, setIsLoading] = useState(false);
  const [isSubmitted, setIsSubmitted] = useState(false);
  const [error, setError] = useState('');

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setIsLoading(true);
    setError('');

    try {
      const response = await fetch('/api/v1/zapomenute-heslo', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ email }),
      });

      if (response.ok) {
        setIsSubmitted(true);
      } else {
        const data = await response.json();
        setError(data.message || 'Došlo k chybě při odesílání emailu.');
      }
    } catch (err) {
      setError('Došlo k chybě při odesílání emailu. Zkuste to prosím znovu.');
    } finally {
      setIsLoading(false);
    }
  };

  if (isSubmitted) {
    return (
      <Layout>
        <div className="flex min-h-screen items-center justify-center bg-gray-50 px-4 py-12 sm:px-6 lg:px-8">
          <div className="w-full max-w-md space-y-8">
            <div className="text-center">
              <div className="mx-auto mb-6 flex h-16 w-16 items-center justify-center rounded-full bg-green-100">
                <CheckCircle className="h-8 w-8 text-green-600" />
              </div>
              <h2 className="mb-4 text-3xl font-bold text-gray-900">
                Email odeslán
              </h2>
              <p className="text-gray-600">
                Pokud je email <strong>{email}</strong> registrován v našem
                systému, odeslali jsme vám instrukce pro obnovení hesla.
              </p>
              <p className="mt-4 text-sm text-gray-500">
                Zkontrolujte také složku spam. Email může dorazit do několika
                minut.
              </p>
            </div>

            <div className="space-y-4">
              <Link href={ROUTES.AUTH.LOGIN}>
                <Button variant="outline" className="w-full">
                  <ArrowLeft className="mr-2 h-4 w-4" />
                  Zpět na přihlášení
                </Button>
              </Link>

              <div className="text-center">
                <button
                  onClick={() => {
                    setIsSubmitted(false);
                    setEmail('');
                  }}
                  className="text-sm text-primary-600 hover:text-primary-500"
                >
                  Zadat jiný email
                </button>
              </div>
            </div>
          </div>
        </div>
      </Layout>
    );
  }

  return (
    <Layout>
      <div className="flex min-h-screen items-center justify-center bg-gray-50 px-4 py-12 sm:px-6 lg:px-8">
        <div className="w-full max-w-md space-y-8">
          <div className="text-center">
            <div className="mx-auto mb-6 flex h-16 w-16 items-center justify-center rounded-full bg-primary-100">
              <Mail className="h-8 w-8 text-primary-600" />
            </div>
            <h2 className="text-3xl font-bold text-gray-900">
              Zapomenuté heslo
            </h2>
            <p className="mt-2 text-gray-600">
              Zadejte svůj email a my vám pošleme instrukce pro obnovení hesla.
            </p>
          </div>

          <form className="space-y-6" onSubmit={handleSubmit}>
            {error && (
              <div className="rounded-md bg-red-50 p-4">
                <div className="text-sm text-red-700">{error}</div>
              </div>
            )}

            <div>
              <label htmlFor="email" className="sr-only">
                Email
              </label>
              <input
                id="email"
                name="email"
                type="email"
                autoComplete="email"
                required
                value={email}
                onChange={(e) => setEmail(e.target.value)}
                className="relative block w-full rounded-md border border-gray-300 px-3 py-2 text-gray-900 placeholder-gray-500 focus:z-10 focus:border-primary-500 focus:outline-none focus:ring-primary-500"
                placeholder="Váš email"
              />
            </div>

            <div>
              <Button
                type="submit"
                className="w-full"
                disabled={isLoading || !email.trim()}
              >
                {isLoading ? 'Odesílám...' : 'Odeslat instrukce'}
              </Button>
            </div>

            <div className="flex items-center justify-between">
              <Link
                href={ROUTES.AUTH.LOGIN}
                className="flex items-center text-sm text-primary-600 hover:text-primary-500"
              >
                <ArrowLeft className="mr-1 h-4 w-4" />
                Zpět na přihlášení
              </Link>

              <Link
                href={ROUTES.AUTH.REGISTER}
                className="text-sm text-primary-600 hover:text-primary-500"
              >
                Nemáte účet?
              </Link>
            </div>
          </form>

          <div className="mt-8 rounded-md bg-blue-50 p-4">
            <div className="text-sm text-blue-700">
              <strong>Tip:</strong> Pokud si heslo nevzpomínáte, můžete se také
              přihlásit pomocí Google nebo Facebook účtu.
            </div>
          </div>
        </div>
      </div>
    </Layout>
  );
}
