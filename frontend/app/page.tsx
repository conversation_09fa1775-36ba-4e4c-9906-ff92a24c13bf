'use client';

import { useState } from 'react';
import { useRouter } from 'next/navigation';
import { Search, MapPin, Shield } from 'lucide-react';
import Layout from '@/components/layout/Layout';
import LoadingLink from '@/components/ui/LoadingLink';
import { ROUTES } from '@/lib/routes';

export default function HomePage() {
  const [searchQuery, setSearchQuery] = useState('');
  const router = useRouter();

  const handleSearch = (e: React.FormEvent) => {
    e.preventDefault();
    if (searchQuery.trim()) {
      router.push(
        `${ROUTES.ITEMS.LIST}?search=${encodeURIComponent(searchQuery.trim())}`
      );
    }
  };

  return (
    <Layout>
      <div className="bg-gray-50">
        {/* Hero Section */}
        <section className="bg-primary-600 py-20 text-white">
          <div className="mx-auto max-w-7xl px-4 text-center sm:px-6 lg:px-8">
            <h2 className="mb-6 text-4xl font-bold md:text-6xl">
              Půj<PERSON>uj si od sousedů
            </h2>
            <p className="mb-8 text-xl text-primary-100 md:text-2xl">
              Moderní platforma pro sdílení věcí ve vašem okolí
            </p>
            <div className="flex flex-col justify-center gap-4 sm:flex-row">
              <LoadingLink
                href={ROUTES.AUTH.REGISTER}
                className="btn bg-white text-primary-600 hover:bg-gray-100"
              >
                Začít půjčovat
              </LoadingLink>
              <LoadingLink
                href={ROUTES.ITEMS.LIST}
                className="btn border-2 border-white text-white hover:bg-white hover:text-primary-600"
              >
                Prohlédnout nabídku
              </LoadingLink>
            </div>
          </div>
        </section>

        {/* Search Section */}
        <section className="bg-white py-16">
          <div className="mx-auto max-w-4xl px-4 sm:px-6 lg:px-8">
            <div className="mb-8 text-center">
              <h3 className="mb-4 text-3xl font-bold text-gray-900">
                Co hledáte?
              </h3>
              <p className="text-lg text-gray-600">
                Najděte přesně to, co potřebujete ve svém okolí
              </p>
            </div>

            <form onSubmit={handleSearch} className="mx-auto max-w-2xl">
              <div className="relative">
                <div className="pointer-events-none absolute inset-y-0 left-0 flex items-center pl-4">
                  <Search className="h-6 w-6 text-gray-400" />
                </div>
                <input
                  type="text"
                  value={searchQuery}
                  onChange={(e) => setSearchQuery(e.target.value)}
                  className="block w-full rounded-lg border border-gray-300 bg-white py-4 pl-12 pr-32 text-lg leading-5 placeholder-gray-500 shadow-sm focus:border-transparent focus:placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-primary-500"
                  placeholder="Vrtačka, sekačka, auto, kolo..."
                />
                <div className="absolute inset-y-0 right-0 flex items-center pr-2">
                  <button
                    type="submit"
                    className="rounded-md bg-primary-600 px-6 py-2 font-medium text-white hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-primary-500 focus:ring-offset-2"
                  >
                    Hledat
                  </button>
                </div>
              </div>
            </form>

            <div className="mt-8 text-center">
              <p className="mb-4 text-sm text-gray-500">Populární kategorie:</p>
              <div className="flex flex-wrap justify-center gap-2">
                <LoadingLink
                  href={`${ROUTES.ITEMS.LIST}?search=vrtačka`}
                  className="rounded-full bg-gray-100 px-4 py-2 text-sm text-gray-700 transition-colors hover:bg-gray-200"
                >
                  Nářadí
                </LoadingLink>
                <LoadingLink
                  href={`${ROUTES.ITEMS.LIST}?search=auto`}
                  className="rounded-full bg-gray-100 px-4 py-2 text-sm text-gray-700 transition-colors hover:bg-gray-200"
                >
                  Doprava
                </LoadingLink>
                <LoadingLink
                  href={`${ROUTES.ITEMS.LIST}?search=kolo`}
                  className="rounded-full bg-gray-100 px-4 py-2 text-sm text-gray-700 transition-colors hover:bg-gray-200"
                >
                  Sport
                </LoadingLink>
                <LoadingLink
                  href={`${ROUTES.ITEMS.LIST}?search=zahrada`}
                  className="rounded-full bg-gray-100 px-4 py-2 text-sm text-gray-700 transition-colors hover:bg-gray-200"
                >
                  Zahrada
                </LoadingLink>
                <LoadingLink
                  href={`${ROUTES.ITEMS.LIST}?search=elektronika`}
                  className="rounded-full bg-gray-100 px-4 py-2 text-sm text-gray-700 transition-colors hover:bg-gray-200"
                >
                  Elektronika
                </LoadingLink>
              </div>
            </div>
          </div>
        </section>

        {/* Features */}
        <section className="bg-gray-50 py-20">
          <div className="mx-auto max-w-7xl px-4 sm:px-6 lg:px-8">
            <h3 className="mb-12 text-center text-3xl font-bold">
              Jak to funguje
            </h3>
            <div className="grid gap-8 md:grid-cols-3">
              <div className="text-center">
                <div className="mx-auto mb-4 flex h-16 w-16 items-center justify-center rounded-full bg-primary-100">
                  <Search className="h-8 w-8 text-primary-600" />
                </div>
                <h4 className="mb-2 text-xl font-semibold">
                  Najdi co potřebuješ
                </h4>
                <p className="text-gray-600">
                  Vyhledej věci ve svém okolí podle kategorie nebo názvu
                </p>
              </div>
              <div className="text-center">
                <div className="mx-auto mb-4 flex h-16 w-16 items-center justify-center rounded-full bg-primary-100">
                  <MapPin className="h-8 w-8 text-primary-600" />
                </div>
                <h4 className="mb-2 text-xl font-semibold">
                  Kontaktuj majitele
                </h4>
                <p className="text-gray-600">
                  Domluvte si podmínky půjčky přímo v aplikaci
                </p>
              </div>
              <div className="text-center">
                <div className="mx-auto mb-4 flex h-16 w-16 items-center justify-center rounded-full bg-primary-100">
                  <Shield className="h-8 w-8 text-primary-600" />
                </div>
                <h4 className="mb-2 text-xl font-semibold">Bezpečně si půjč</h4>
                <p className="text-gray-600">
                  Systém hodnocení a digitální smlouvy zajistí bezpečnost
                </p>
              </div>
            </div>
          </div>
        </section>

        {/* CTA Section */}
        <section className="bg-gray-100 py-20">
          <div className="mx-auto max-w-7xl px-4 text-center sm:px-6 lg:px-8">
            <h3 className="mb-4 text-3xl font-bold">Připoj se ke komunitě</h3>
            <p className="mb-8 text-xl text-gray-600">
              Tisíce lidí už sdílí své věci a šetří peníze
            </p>
            <div className="mb-8 flex items-center justify-center space-x-8">
              <div className="text-center">
                <div className="text-3xl font-bold text-primary-600">1000+</div>
                <div className="text-gray-600">Aktivních uživatelů</div>
              </div>
              <div className="text-center">
                <div className="text-3xl font-bold text-primary-600">5000+</div>
                <div className="text-gray-600">Dostupných věcí</div>
              </div>
              <div className="text-center">
                <div className="text-3xl font-bold text-primary-600">4.8</div>
                <div className="text-gray-600">Průměrné hodnocení</div>
              </div>
            </div>
            <LoadingLink
              href={ROUTES.AUTH.REGISTER}
              className="btn btn-primary px-8 py-3 text-lg"
            >
              Registrovat se zdarma
            </LoadingLink>
          </div>
        </section>
      </div>
    </Layout>
  );
}
