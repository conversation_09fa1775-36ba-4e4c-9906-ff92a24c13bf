'use client';

import { useState } from 'react';
import Link from 'next/link';
import Layout from '@/components/layout/Layout';
import {
  Search,
  ChevronDown,
  ChevronUp,
  HelpCircle,
  MessageCircle,
  Mail,
  Phone,
  Book,
  Users,
  Shield,
  CreditCard,
} from 'lucide-react';
import { ROUTES } from '@/lib/routes';

interface FAQItem {
  id: string;
  question: string;
  answer: string;
  category: string;
}

const faqData: FAQItem[] = [
  {
    id: '1',
    question: 'Jak se registruji na platformu?',
    answer:
      'Registrace je velmi jednoduchá. Klikněte na tlačítko "Registrace" v pravém horním rohu, vyplňte základní údaje nebo se přihlaste pomocí Google/Facebook účtu. Po ověření emailu můžete začít používat všechny funkce platformy.',
    category: 'Začínáme',
  },
  {
    id: '2',
    question: 'Je půjčování věcí bezpečné?',
    answer:
      'Ano, bezpečnost je naší prioritou. Všichni uživatelé procházejí ověřením identity, máme systém hodnocení a recenzí, a nabízíme podporu při řešení sporů. Doporučujeme také osobní předání věcí a kontrolu jejich stavu.',
    category: 'Bezpečnost',
  },
  {
    id: '3',
    question: 'Jak fungují platby?',
    answer:
      'Platby probíhají bezpečně přes naši platformu. Podporujeme platební karty, bankovní převody a online platby. Peníze jsou uvolněny půjčovateli až po potvrzení úspěšného předání věci.',
    category: 'Platby',
  },
  {
    id: '4',
    question: 'Co když se věc poškodí během půjčení?',
    answer:
      'V případě poškození věci kontaktujte naši podporu do 24 hodin. Máme systém pro řešení sporů a v závažných případech nabízíme pojištění. Doporučujeme vždy zdokumentovat stav věci před předáním.',
    category: 'Problémy',
  },
  {
    id: '5',
    question: 'Mohu zrušit rezervaci?',
    answer:
      'Ano, rezervaci můžete zrušit až do 24 hodin před dohodnutým termínem předání bez poplatku. Při pozdějším zrušení může být účtován poplatek podle podmínek půjčovatele.',
    category: 'Rezervace',
  },
  {
    id: '6',
    question: 'Jak nastavím cenu za půjčení?',
    answer:
      'Při přidávání věci můžete nastavit cenu za den, týden nebo měsíc. Doporučujeme podívat se na podobné věci v okolí a nastavit konkurenceschopnou cenu. Můžete ji kdykoli změnit.',
    category: 'Půjčování',
  },
];

const categories = [
  'Všechny',
  'Začínáme',
  'Bezpečnost',
  'Platby',
  'Problémy',
  'Rezervace',
  'Půjčování',
];

export default function HelpPage() {
  const [searchQuery, setSearchQuery] = useState('');
  const [selectedCategory, setSelectedCategory] = useState('Všechny');
  const [openFAQ, setOpenFAQ] = useState<string | null>(null);

  const filteredFAQ = faqData.filter((item) => {
    const matchesSearch =
      item.question.toLowerCase().includes(searchQuery.toLowerCase()) ||
      item.answer.toLowerCase().includes(searchQuery.toLowerCase());
    const matchesCategory =
      selectedCategory === 'Všechny' || item.category === selectedCategory;
    return matchesSearch && matchesCategory;
  });

  const toggleFAQ = (id: string) => {
    setOpenFAQ(openFAQ === id ? null : id);
  };

  return (
    <Layout>
      <div className="bg-gray-50">
        {/* Hero Section */}
        <section className="bg-primary-600 py-16 text-white">
          <div className="mx-auto max-w-7xl px-4 sm:px-6 lg:px-8">
            <div className="text-center">
              <h1 className="mb-6 text-4xl font-bold md:text-5xl">
                Nápověda a podpora
              </h1>
              <p className="mx-auto max-w-3xl text-xl text-primary-100">
                Najděte odpovědi na své otázky nebo nás kontaktujte pro osobní
                pomoc
              </p>
            </div>
          </div>
        </section>

        {/* Search and Categories */}
        <section className="py-8">
          <div className="mx-auto max-w-4xl px-4 sm:px-6 lg:px-8">
            {/* Search */}
            <div className="mb-8">
              <div className="relative">
                <Search className="absolute left-3 top-1/2 h-5 w-5 -translate-y-1/2 text-gray-400" />
                <input
                  type="text"
                  placeholder="Hledejte v nápovědě..."
                  value={searchQuery}
                  onChange={(e) => setSearchQuery(e.target.value)}
                  className="w-full rounded-lg border border-gray-300 py-3 pl-10 pr-4 focus:border-primary-500 focus:outline-none focus:ring-1 focus:ring-primary-500"
                />
              </div>
            </div>

            {/* Categories */}
            <div className="mb-8">
              <div className="flex flex-wrap gap-2">
                {categories.map((category) => (
                  <button
                    key={category}
                    onClick={() => setSelectedCategory(category)}
                    className={`rounded-full px-4 py-2 text-sm font-medium transition-colors ${
                      selectedCategory === category
                        ? 'bg-primary-600 text-white'
                        : 'bg-white text-gray-700 hover:bg-gray-100'
                    }`}
                  >
                    {category}
                  </button>
                ))}
              </div>
            </div>
          </div>
        </section>

        {/* FAQ Section */}
        <section className="pb-16">
          <div className="mx-auto max-w-4xl px-4 sm:px-6 lg:px-8">
            <div className="mb-8">
              <h2 className="text-2xl font-bold text-gray-900">
                Často kladené otázky
              </h2>
              <p className="mt-2 text-gray-600">
                Najděte rychlé odpovědi na nejčastější otázky
              </p>
            </div>

            <div className="space-y-4">
              {filteredFAQ.map((item) => (
                <div key={item.id} className="rounded-lg bg-white shadow-sm">
                  <button
                    onClick={() => toggleFAQ(item.id)}
                    className="flex w-full items-center justify-between p-6 text-left"
                  >
                    <span className="font-medium text-gray-900">
                      {item.question}
                    </span>
                    {openFAQ === item.id ? (
                      <ChevronUp className="h-5 w-5 text-gray-500" />
                    ) : (
                      <ChevronDown className="h-5 w-5 text-gray-500" />
                    )}
                  </button>
                  {openFAQ === item.id && (
                    <div className="border-t px-6 pb-6 pt-4">
                      <p className="text-gray-600">{item.answer}</p>
                    </div>
                  )}
                </div>
              ))}
            </div>

            {filteredFAQ.length === 0 && (
              <div className="py-12 text-center">
                <HelpCircle className="mx-auto mb-4 h-12 w-12 text-gray-400" />
                <h3 className="mb-2 text-lg font-medium text-gray-900">
                  Nenašli jsme odpověď
                </h3>
                <p className="text-gray-600">
                  Zkuste jiné hledané výrazy nebo nás kontaktujte přímo
                </p>
              </div>
            )}
          </div>
        </section>

        {/* Quick Help */}
        <section className="bg-white py-16">
          <div className="mx-auto max-w-7xl px-4 sm:px-6 lg:px-8">
            <div className="text-center">
              <h2 className="mb-12 text-3xl font-bold text-gray-900">
                Rychlá pomoc
              </h2>
            </div>

            <div className="grid grid-cols-1 gap-8 md:grid-cols-2 lg:grid-cols-4">
              <div className="text-center">
                <div className="mx-auto mb-6 flex h-16 w-16 items-center justify-center rounded-full bg-blue-100">
                  <Book className="h-8 w-8 text-blue-600" />
                </div>
                <h3 className="mb-4 text-lg font-semibold text-gray-900">
                  Průvodce
                </h3>
                <p className="mb-4 text-gray-600">
                  Kompletní návod jak používat platformu
                </p>
                <Link
                  href={ROUTES.STATIC.HOW_IT_WORKS}
                  className="text-primary-600 hover:text-primary-500"
                >
                  Jak to funguje →
                </Link>
              </div>

              <div className="text-center">
                <div className="mx-auto mb-6 flex h-16 w-16 items-center justify-center rounded-full bg-green-100">
                  <Shield className="h-8 w-8 text-green-600" />
                </div>
                <h3 className="mb-4 text-lg font-semibold text-gray-900">
                  Bezpečnost
                </h3>
                <p className="mb-4 text-gray-600">
                  Tipy pro bezpečné půjčování
                </p>
                <Link
                  href={ROUTES.STATIC.SAFETY}
                  className="text-primary-600 hover:text-primary-500"
                >
                  Bezpečnostní tipy →
                </Link>
              </div>

              <div className="text-center">
                <div className="mx-auto mb-6 flex h-16 w-16 items-center justify-center rounded-full bg-yellow-100">
                  <Users className="h-8 w-8 text-yellow-600" />
                </div>
                <h3 className="mb-4 text-lg font-semibold text-gray-900">
                  Komunita
                </h3>
                <p className="mb-4 text-gray-600">
                  Diskutujte s ostatními uživateli
                </p>
                <a href="#" className="text-primary-600 hover:text-primary-500">
                  Komunitní fórum →
                </a>
              </div>

              <div className="text-center">
                <div className="mx-auto mb-6 flex h-16 w-16 items-center justify-center rounded-full bg-purple-100">
                  <MessageCircle className="h-8 w-8 text-purple-600" />
                </div>
                <h3 className="mb-4 text-lg font-semibold text-gray-900">
                  Podpora
                </h3>
                <p className="mb-4 text-gray-600">
                  Kontaktujte náš tým podpory
                </p>
                <Link
                  href={ROUTES.STATIC.CONTACT}
                  className="text-primary-600 hover:text-primary-500"
                >
                  Kontaktovat nás →
                </Link>
              </div>
            </div>
          </div>
        </section>

        {/* Contact Support */}
        <section className="py-16">
          <div className="mx-auto max-w-4xl px-4 sm:px-6 lg:px-8">
            <div className="rounded-lg bg-primary-50 p-8 text-center">
              <h2 className="mb-4 text-2xl font-bold text-gray-900">
                Stále potřebujete pomoc?
              </h2>
              <p className="mb-8 text-gray-600">
                Náš tým podpory je připraven vám pomoci s jakýmkoliv problémem
              </p>
              <div className="flex flex-col justify-center gap-4 sm:flex-row">
                <Link
                  href={ROUTES.STATIC.CONTACT}
                  className="btn btn-primary inline-flex items-center"
                >
                  <Mail className="mr-2 h-4 w-4" />
                  Napsat email
                </Link>
                <a
                  href="tel:+420123456789"
                  className="btn btn-secondary inline-flex items-center"
                >
                  <Phone className="mr-2 h-4 w-4" />
                  Zavolat: +420 123 456 789
                </a>
              </div>
            </div>
          </div>
        </section>
      </div>
    </Layout>
  );
}
