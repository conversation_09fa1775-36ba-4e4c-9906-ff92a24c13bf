'use client';

import { useEffect, useState } from 'react';
import { useSearchParams, useRouter } from 'next/navigation';
import Link from 'next/link';
import { ROUTES } from '@/lib/routes';
import axios from 'axios';

export default function EmailVerificationPage() {
  const searchParams = useSearchParams();
  const router = useRouter();
  const [status, setStatus] = useState<'loading' | 'success' | 'error'>(
    'loading'
  );
  const [message, setMessage] = useState('');

  useEffect(() => {
    // Ujisti se, že jsme na klientovi
    if (typeof window === 'undefined') return;

    const token = searchParams.get('token');

    if (!token) {
      setStatus('error');
      setMessage('Chybí ověřovací token. Zkontrolujte odkaz v emailu.');
      return;
    }

    // Použij globální flag pro zabránění dvojitému volání
    const globalKey = `__email_verification_${token}`;

    // @ts-ignore
    if (window[globalKey]) {
      return; // Už se zpracovává nebo bylo zpracováno
    }

    // @ts-ignore
    window[globalKey] = true;

    const verifyEmail = async () => {
      try {
        const apiUrl =
          process.env.NEXT_PUBLIC_API_URL || `${window.location.origin}/api/v1`;
        const response = await axios.post(`${apiUrl}/overeni-emailu`, {
          token,
        });

        setStatus('success');
        setMessage(response.data.message || 'Email byl úspěšně ověřen!');

        // Po 3 sekundách přesměruj na přihlášení
        setTimeout(() => {
          router.push(ROUTES.AUTH.LOGIN);
        }, 3000);
      } catch (error: any) {
        setStatus('error');
        setMessage(
          error.response?.data?.error ||
            'Nastala chyba při ověřování emailu. Zkuste to znovu.'
        );
      }
    };

    verifyEmail();
  }, [searchParams, router]);

  return (
    <div className="flex min-h-screen flex-col justify-center bg-gray-50 py-12 sm:px-6 lg:px-8">
      <div className="sm:mx-auto sm:w-full sm:max-w-md">
        <Link href="/" className="flex justify-center">
          <h1 className="text-3xl font-bold text-primary-600">Pujčovna</h1>
        </Link>
        <h2 className="mt-6 text-center text-3xl font-extrabold text-gray-900">
          Ověření emailu
        </h2>
      </div>

      <div className="mt-8 sm:mx-auto sm:w-full sm:max-w-md">
        <div className="bg-white px-4 py-8 shadow sm:rounded-lg sm:px-10">
          {status === 'loading' && (
            <div className="text-center">
              <div className="inline-block h-8 w-8 animate-spin rounded-full border-4 border-solid border-primary-600 border-r-transparent"></div>
              <p className="mt-4 text-gray-600">Ověřuji váš email...</p>
            </div>
          )}

          {status === 'success' && (
            <div className="text-center">
              <div className="mx-auto flex h-12 w-12 items-center justify-center rounded-full bg-green-100">
                <svg
                  className="h-6 w-6 text-green-600"
                  fill="none"
                  stroke="currentColor"
                  viewBox="0 0 24 24"
                >
                  <path
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    strokeWidth={2}
                    d="M5 13l4 4L19 7"
                  />
                </svg>
              </div>
              <h3 className="mt-4 text-lg font-medium text-gray-900">
                Email ověřen!
              </h3>
              <p className="mt-2 text-sm text-gray-600">{message}</p>
              <p className="mt-4 text-sm text-gray-500">
                Budete automaticky přesměrováni na přihlášení za 3 sekundy...
              </p>
              <div className="mt-6">
                <Link
                  href={ROUTES.AUTH.LOGIN}
                  className="btn btn-primary w-full"
                >
                  Přihlásit se nyní
                </Link>
              </div>
            </div>
          )}

          {status === 'error' && (
            <div className="text-center">
              <div className="mx-auto flex h-12 w-12 items-center justify-center rounded-full bg-red-100">
                <svg
                  className="h-6 w-6 text-red-600"
                  fill="none"
                  stroke="currentColor"
                  viewBox="0 0 24 24"
                >
                  <path
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    strokeWidth={2}
                    d="M6 18L18 6M6 6l12 12"
                  />
                </svg>
              </div>
              <h3 className="mt-4 text-lg font-medium text-gray-900">
                Chyba při ověřování
              </h3>
              <p className="mt-2 text-sm text-gray-600">{message}</p>
              <div className="mt-6 space-y-3">
                <div>
                  <Link
                    href={ROUTES.AUTH.REGISTER}
                    className="btn btn-primary w-full"
                  >
                    Registrovat se znovu
                  </Link>
                </div>
                <div>
                  <Link href={ROUTES.HOME} className="btn btn-secondary w-full">
                    Zpět na hlavní stránku
                  </Link>
                </div>
              </div>
            </div>
          )}
        </div>
      </div>
    </div>
  );
}
