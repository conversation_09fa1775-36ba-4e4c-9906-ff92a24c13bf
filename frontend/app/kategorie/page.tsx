'use client';

import { useState } from 'react';
import Link from 'next/link';
import Layout from '@/components/layout/Layout';
import {
  Wrench,
  Home,
  Car,
  Gamepad2,
  Camera,
  Dumbbell,
  Baby,
  Music,
  Laptop,
  Shirt,
  Search,
  ArrowRight,
  TrendingUp,
} from 'lucide-react';
import { ROUTES } from '@/lib/routes';

interface Category {
  id: string;
  name: string;
  description: string;
  icon: React.ReactNode;
  itemCount: number;
  trending?: boolean;
  subcategories: string[];
}

const categories: Category[] = [
  {
    id: 'tools',
    name: 'Nástroje a nářadí',
    description: '<PERSON><PERSON><PERSON><PERSON>, pily, kladiva a další nástroje pro domácí kutily',
    icon: <Wrench className="h-8 w-8" />,
    itemCount: 1250,
    trending: true,
    subcategories: [
      'Elektrické nástroje',
      '<PERSON>u<PERSON>n<PERSON> nástroje',
      '<PERSON><PERSON>rad<PERSON><PERSON> nářadí',
      '<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> p<PERSON>tro<PERSON>',
    ],
  },
  {
    id: 'home',
    name: '<PERSON><PERSON><PERSON><PERSON><PERSON>',
    description: '<PERSON>řeb<PERSON><PERSON><PERSON>, nábytek a vybavení pro domácnost',
    icon: <Home className="h-8 w-8" />,
    itemCount: 890,
    subcategories: [
      'Kuchyňské spotřebiče',
      'Čisticí technika',
      'Nábytek',
      'Dekorace',
    ],
  },
  {
    id: 'automotive',
    name: 'Auto a doprava',
    description: 'Autodoplňky, náhradní díly a dopravní prostředky',
    icon: <Car className="h-8 w-8" />,
    itemCount: 650,
    subcategories: [
      'Autodoplňky',
      'Náhradní díly',
      'Kola a pneumatiky',
      'Přívěsy',
    ],
  },
  {
    id: 'sports',
    name: 'Sport a fitness',
    description: 'Sportovní vybavení, fitness pomůcky a outdoor gear',
    icon: <Dumbbell className="h-8 w-8" />,
    itemCount: 780,
    trending: true,
    subcategories: [
      'Fitness vybavení',
      'Outdoor aktivity',
      'Zimní sporty',
      'Vodní sporty',
    ],
  },
  {
    id: 'electronics',
    name: 'Elektronika',
    description: 'Počítače, telefony, audio a video technika',
    icon: <Laptop className="h-8 w-8" />,
    itemCount: 420,
    subcategories: ['Počítače', 'Audio/Video', 'Telefony', 'Herní konzole'],
  },
  {
    id: 'photography',
    name: 'Foto a video',
    description: 'Fotoaparáty, objektivy, stativy a video technika',
    icon: <Camera className="h-8 w-8" />,
    itemCount: 340,
    subcategories: ['Fotoaparáty', 'Objektivy', 'Stativy', 'Osvětlení'],
  },
  {
    id: 'gaming',
    name: 'Hry a zábava',
    description: 'Herní konzole, deskové hry a zábavní elektronika',
    icon: <Gamepad2 className="h-8 w-8" />,
    itemCount: 290,
    subcategories: ['Herní konzole', 'Deskové hry', 'Puzzle', 'Párty hry'],
  },
  {
    id: 'music',
    name: 'Hudba',
    description: 'Hudební nástroje, audio technika a DJ vybavení',
    icon: <Music className="h-8 w-8" />,
    itemCount: 180,
    subcategories: [
      'Hudební nástroje',
      'Audio technika',
      'DJ vybavení',
      'Nahrávací technika',
    ],
  },
  {
    id: 'baby',
    name: 'Děti a miminka',
    description: 'Dětské vybavení, hračky a pomůcky pro rodiče',
    icon: <Baby className="h-8 w-8" />,
    itemCount: 520,
    subcategories: ['Dětské vybavení', 'Hračky', 'Autosedačky', 'Kočárky'],
  },
  {
    id: 'fashion',
    name: 'Móda a oblečení',
    description: 'Oblečení, doplňky a módní accessoires',
    icon: <Shirt className="h-8 w-8" />,
    itemCount: 310,
    subcategories: ['Oblečení', 'Boty', 'Doplňky', 'Šperky'],
  },
];

export default function CategoriesPage() {
  const [searchQuery, setSearchQuery] = useState('');

  const filteredCategories = categories.filter(
    (category) =>
      category.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
      category.description.toLowerCase().includes(searchQuery.toLowerCase()) ||
      category.subcategories.some((sub) =>
        sub.toLowerCase().includes(searchQuery.toLowerCase())
      )
  );

  const totalItems = categories.reduce((sum, cat) => sum + cat.itemCount, 0);
  const trendingCategories = categories.filter((cat) => cat.trending);

  return (
    <Layout>
      <div className="bg-gray-50">
        {/* Hero Section */}
        <section className="bg-primary-600 py-16 text-white">
          <div className="mx-auto max-w-7xl px-4 sm:px-6 lg:px-8">
            <div className="text-center">
              <h1 className="mb-6 text-4xl font-bold md:text-5xl">
                Kategorie předmětů
              </h1>
              <p className="mx-auto max-w-3xl text-xl text-primary-100">
                Procházejte tisíce věcí rozdělených do kategorií. Najděte přesně
                to, co hledáte.
              </p>
              <div className="mt-8 text-center">
                <div className="inline-flex items-center rounded-full bg-primary-500 px-6 py-2">
                  <span className="text-lg font-semibold">
                    {totalItems.toLocaleString()}
                  </span>
                  <span className="ml-2">dostupných předmětů</span>
                </div>
              </div>
            </div>
          </div>
        </section>

        {/* Search */}
        <section className="py-8">
          <div className="mx-auto max-w-4xl px-4 sm:px-6 lg:px-8">
            <div className="relative">
              <Search className="absolute left-3 top-1/2 h-5 w-5 -translate-y-1/2 text-gray-400" />
              <input
                type="text"
                placeholder="Hledejte v kategoriích..."
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
                className="w-full rounded-lg border border-gray-300 py-3 pl-10 pr-4 focus:border-primary-500 focus:outline-none focus:ring-1 focus:ring-primary-500"
              />
            </div>
          </div>
        </section>

        {/* Trending Categories */}
        {!searchQuery && (
          <section className="py-8">
            <div className="mx-auto max-w-7xl px-4 sm:px-6 lg:px-8">
              <div className="mb-6 flex items-center">
                <TrendingUp className="mr-2 h-6 w-6 text-orange-600" />
                <h2 className="text-2xl font-bold text-gray-900">
                  Populární kategorie
                </h2>
              </div>
              <div className="grid grid-cols-1 gap-6 md:grid-cols-2">
                {trendingCategories.map((category) => (
                  <Link
                    key={category.id}
                    href={`${ROUTES.ITEMS.LIST}?category=${category.id}`}
                    className="group relative overflow-hidden rounded-lg bg-gradient-to-r from-orange-500 to-red-500 p-6 text-white transition-transform hover:scale-105"
                  >
                    <div className="flex items-center justify-between">
                      <div>
                        <div className="mb-2 flex items-center">
                          {category.icon}
                          <span className="ml-3 text-xl font-semibold">
                            {category.name}
                          </span>
                        </div>
                        <p className="mb-4 text-orange-100">
                          {category.description}
                        </p>
                        <div className="text-sm">
                          <span className="font-medium">
                            {category.itemCount}
                          </span>{' '}
                          předmětů
                        </div>
                      </div>
                      <ArrowRight className="h-6 w-6 transition-transform group-hover:translate-x-1" />
                    </div>
                  </Link>
                ))}
              </div>
            </div>
          </section>
        )}

        {/* All Categories */}
        <section className="pb-16">
          <div className="mx-auto max-w-7xl px-4 sm:px-6 lg:px-8">
            <h2 className="mb-8 text-2xl font-bold text-gray-900">
              {searchQuery ? 'Výsledky vyhledávání' : 'Všechny kategorie'}
            </h2>

            {filteredCategories.length > 0 ? (
              <div className="grid grid-cols-1 gap-6 md:grid-cols-2 lg:grid-cols-3">
                {filteredCategories.map((category) => (
                  <Link
                    key={category.id}
                    href={`${ROUTES.ITEMS.LIST}?category=${category.id}`}
                    className="group rounded-lg bg-white p-6 shadow-sm transition-all hover:-translate-y-1 hover:shadow-md"
                  >
                    <div className="mb-4 flex items-center justify-between">
                      <div className="flex h-12 w-12 items-center justify-center rounded-full bg-primary-100 text-primary-600">
                        {category.icon}
                      </div>
                      {category.trending && (
                        <span className="rounded-full bg-orange-100 px-2 py-1 text-xs font-medium text-orange-800">
                          Populární
                        </span>
                      )}
                    </div>

                    <h3 className="mb-2 text-lg font-semibold text-gray-900 group-hover:text-primary-600">
                      {category.name}
                    </h3>

                    <p className="mb-4 text-sm text-gray-600">
                      {category.description}
                    </p>

                    <div className="mb-4 text-sm text-gray-500">
                      <span className="font-medium text-gray-900">
                        {category.itemCount}
                      </span>{' '}
                      předmětů
                    </div>

                    <div className="space-y-1">
                      <div className="text-xs font-medium text-gray-500">
                        Podkategorie:
                      </div>
                      <div className="flex flex-wrap gap-1">
                        {category.subcategories
                          .slice(0, 3)
                          .map((sub, index) => (
                            <span
                              key={index}
                              className="rounded-full bg-gray-100 px-2 py-1 text-xs text-gray-600"
                            >
                              {sub}
                            </span>
                          ))}
                        {category.subcategories.length > 3 && (
                          <span className="rounded-full bg-gray-100 px-2 py-1 text-xs text-gray-600">
                            +{category.subcategories.length - 3}
                          </span>
                        )}
                      </div>
                    </div>

                    <div className="mt-4 flex items-center text-primary-600 group-hover:text-primary-700">
                      <span className="text-sm font-medium">Prohlédnout</span>
                      <ArrowRight className="ml-1 h-4 w-4 transition-transform group-hover:translate-x-1" />
                    </div>
                  </Link>
                ))}
              </div>
            ) : (
              <div className="py-12 text-center">
                <Search className="mx-auto mb-4 h-12 w-12 text-gray-400" />
                <h3 className="mb-2 text-lg font-medium text-gray-900">
                  Nenašli jsme žádné kategorie
                </h3>
                <p className="text-gray-600">
                  Zkuste jiné hledané výrazy nebo procházejte všechny kategorie
                </p>
                <button
                  onClick={() => setSearchQuery('')}
                  className="mt-4 text-primary-600 hover:text-primary-500"
                >
                  Zobrazit všechny kategorie
                </button>
              </div>
            )}
          </div>
        </section>

        {/* CTA Section */}
        <section className="bg-white py-16">
          <div className="mx-auto max-w-7xl px-4 sm:px-6 lg:px-8">
            <div className="text-center">
              <h2 className="mb-4 text-2xl font-bold text-gray-900">
                Nenašli jste, co hledáte?
              </h2>
              <p className="mb-8 text-gray-600">
                Zkuste vyhledávání nebo procházejte všechny dostupné předměty
              </p>
              <div className="flex flex-col justify-center gap-4 sm:flex-row">
                <Link href={ROUTES.ITEMS.LIST} className="btn btn-primary">
                  Všechny předměty
                </Link>
                <Link href={ROUTES.ITEMS.CREATE} className="btn btn-secondary">
                  Přidat svou věc
                </Link>
              </div>
            </div>
          </div>
        </section>
      </div>
    </Layout>
  );
}
