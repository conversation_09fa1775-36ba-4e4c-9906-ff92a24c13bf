'use client';

import { useState } from 'react';
import Layout from '@/components/layout/Layout';
import { ChevronDown, ChevronUp, Search, HelpCircle } from 'lucide-react';

interface FAQItem {
  id: string;
  question: string;
  answer: string;
  category: string;
}

const faqData: FAQItem[] = [
  {
    id: '1',
    question: 'Co je Pujčovna a jak funguje?',
    answer:
      'Pujčovna je platforma pro sdílení věcí mezi sousedy. Umožňuje vám půjčovat si věci, které potřebujete jen občas, od lidí ve vašem okolí, nebo naopak půjčovat své věci ostatním a vydělávat na nich.',
    category: 'Základy',
  },
  {
    id: '2',
    question: 'Je registrace zdarma?',
    answer:
      'Ano, registrace je zcela zdarma. <PERSON>latky se účtují pouze při úspěšném půjčení - mal<PERSON> provize z ceny půjčení.',
    category: '<PERSON><PERSON>lady',
  },
  {
    id: '3',
    question: 'J<PERSON> se registruji?',
    answer:
      'Klikněte na "Registrace" v pravém horním rohu, vyplňte základní údaje nebo se přihlaste pomocí Google/Facebook účtu. Po ověření emailu můžete začít používat platformu.',
    category: 'Registrace',
  },
  {
    id: '4',
    question: 'Mohu se přihlásit pomocí sociálních sítí?',
    answer:
      'Ano, podporujeme přihlášení pomocí Google a Facebook účtů pro rychlejší a jednodušší registraci.',
    category: 'Registrace',
  },
  {
    id: '5',
    question: 'Jak najdu věc, kterou potřebuji?',
    answer:
      'Použijte vyhledávání na hlavní stránce nebo procházejte kategorie. Můžete filtrovat podle vzdálenosti, ceny a dostupnosti.',
    category: 'Půjčování',
  },
  {
    id: '6',
    question: 'Jak dlouho si mohu věc půjčit?',
    answer:
      'Doba půjčení závisí na dohodě s majitelem. Obvykle je to od několika hodin po několik týdnů. Podmínky jsou uvedeny u každé věci.',
    category: 'Půjčování',
  },
  {
    id: '7',
    question: 'Co když potřebuji věc déle, než jsem původně plánoval?',
    answer:
      'Kontaktujte majitele a domluvte se na prodloužení. Pokud souhlasí, můžete půjčení prodloužit přímo v aplikaci.',
    category: 'Půjčování',
  },
  {
    id: '8',
    question: 'Jak přidám svou věc k půjčování?',
    answer:
      'Po přihlášení klikněte na "Přidat věc", vyplňte popis, přidejte fotografie, nastavte cenu a podmínky půjčení.',
    category: 'Půjčování věcí',
  },
  {
    id: '9',
    question: 'Jak nastavím cenu za půjčení?',
    answer:
      'Můžete nastavit cenu za den, týden nebo měsíc. Doporučujeme podívat se na podobné věci v okolí a nastavit konkurenceschopnou cenu.',
    category: 'Půjčování věcí',
  },
  {
    id: '10',
    question: 'Mohu odmítnout žádost o půjčení?',
    answer:
      'Ano, jako majitel máte právo rozhodnout, komu svou věc půjčíte. Můžete žádost schválit nebo zdvořile odmítnout.',
    category: 'Půjčování věcí',
  },
  {
    id: '11',
    question: 'Jak fungují platby?',
    answer:
      'Platby probíhají bezpečně přes naši platformu. Podporujeme platební karty a bankovní převody. Peníze jsou uvolněny majiteli po potvrzení předání.',
    category: 'Platby',
  },
  {
    id: '12',
    question: 'Kdy dostanu peníze za půjčení?',
    answer:
      'Peníze jsou uvolněny na váš účet do 24 hodin po potvrzení úspěšného předání věci půjčovateli.',
    category: 'Platby',
  },
  {
    id: '13',
    question: 'Jaké jsou poplatky?',
    answer:
      'Účtujeme malou provizi z každého úspěšného půjčení (obvykle 5-10%). Registrace a prohlížení nabídky je zdarma.',
    category: 'Platby',
  },
  {
    id: '14',
    question: 'Je půjčování bezpečné?',
    answer:
      'Ano, máme několik bezpečnostních opatření: ověření uživatelů, systém hodnocení, možnost nahlášení problémů a podporu při řešení sporů.',
    category: 'Bezpečnost',
  },
  {
    id: '15',
    question: 'Co když se věc poškodí?',
    answer:
      'V případě poškození kontaktujte naši podporu do 24 hodin. Máme systém pro řešení sporů a v závažných případech nabízíme pojištění.',
    category: 'Bezpečnost',
  },
  {
    id: '16',
    question: 'Jak poznám, že je uživatel důvěryhodný?',
    answer:
      'Každý uživatel má profil s hodnocením od ostatních, počtem úspěšných půjček a ověřením identity. Doporučujeme číst recenze.',
    category: 'Bezpečnost',
  },
  {
    id: '17',
    question: 'Mohu zrušit rezervaci?',
    answer:
      'Ano, rezervaci můžete zrušit až do 24 hodin před dohodnutým termínem bez poplatku. Při pozdějším zrušení může být účtován poplatek.',
    category: 'Rezervace',
  },
  {
    id: '18',
    question: 'Co když se majitel neukáže k předání?',
    answer:
      'Pokud se majitel neukáže bez předchozího upozornění, kontaktujte naši podporu. Pomůžeme vyřešit situaci a případně vrátit peníze.',
    category: 'Rezervace',
  },
  {
    id: '19',
    question: 'Funguje aplikace i na mobilu?',
    answer:
      'Ano, naše webová aplikace je optimalizovaná pro mobilní zařízení. Pracujeme také na nativní mobilní aplikaci.',
    category: 'Technické',
  },
  {
    id: '20',
    question: 'Jak vás mohu kontaktovat?',
    answer:
      'Můžete nás kontaktovat <NAME_EMAIL>, telefonicky na +420 123 456 789 nebo přes kontaktní formulář na webu.',
    category: 'Podpora',
  },
];

const categories = [
  'Všechny',
  'Základy',
  'Registrace',
  'Půjčování',
  'Půjčování věcí',
  'Platby',
  'Bezpečnost',
  'Rezervace',
  'Technické',
  'Podpora',
];

export default function FAQPage() {
  const [searchQuery, setSearchQuery] = useState('');
  const [selectedCategory, setSelectedCategory] = useState('Všechny');
  const [openFAQ, setOpenFAQ] = useState<string | null>(null);

  const filteredFAQ = faqData.filter((item) => {
    const matchesSearch =
      item.question.toLowerCase().includes(searchQuery.toLowerCase()) ||
      item.answer.toLowerCase().includes(searchQuery.toLowerCase());
    const matchesCategory =
      selectedCategory === 'Všechny' || item.category === selectedCategory;
    return matchesSearch && matchesCategory;
  });

  const toggleFAQ = (id: string) => {
    setOpenFAQ(openFAQ === id ? null : id);
  };

  return (
    <Layout>
      <div className="bg-gray-50">
        {/* Hero Section */}
        <section className="bg-primary-600 py-16 text-white">
          <div className="mx-auto max-w-7xl px-4 sm:px-6 lg:px-8">
            <div className="text-center">
              <h1 className="mb-6 text-4xl font-bold md:text-5xl">
                Často kladené otázky
              </h1>
              <p className="mx-auto max-w-3xl text-xl text-primary-100">
                Najděte rychlé odpovědi na nejčastější otázky o používání
                platformy Pujčovna
              </p>
            </div>
          </div>
        </section>

        {/* Search and Filter */}
        <section className="py-8">
          <div className="mx-auto max-w-4xl px-4 sm:px-6 lg:px-8">
            {/* Search */}
            <div className="mb-8">
              <div className="relative">
                <Search className="absolute left-3 top-1/2 h-5 w-5 -translate-y-1/2 text-gray-400" />
                <input
                  type="text"
                  placeholder="Hledejte v otázkách a odpovědích..."
                  value={searchQuery}
                  onChange={(e) => setSearchQuery(e.target.value)}
                  className="w-full rounded-lg border border-gray-300 py-3 pl-10 pr-4 focus:border-primary-500 focus:outline-none focus:ring-1 focus:ring-primary-500"
                />
              </div>
            </div>

            {/* Categories */}
            <div className="mb-8">
              <div className="flex flex-wrap gap-2">
                {categories.map((category) => (
                  <button
                    key={category}
                    onClick={() => setSelectedCategory(category)}
                    className={`rounded-full px-4 py-2 text-sm font-medium transition-colors ${
                      selectedCategory === category
                        ? 'bg-primary-600 text-white'
                        : 'bg-white text-gray-700 hover:bg-gray-100'
                    }`}
                  >
                    {category}
                  </button>
                ))}
              </div>
            </div>
          </div>
        </section>

        {/* FAQ List */}
        <section className="pb-16">
          <div className="mx-auto max-w-4xl px-4 sm:px-6 lg:px-8">
            {filteredFAQ.length > 0 ? (
              <div className="space-y-4">
                {filteredFAQ.map((item) => (
                  <div key={item.id} className="rounded-lg bg-white shadow-sm">
                    <button
                      onClick={() => toggleFAQ(item.id)}
                      className="flex w-full items-center justify-between p-6 text-left hover:bg-gray-50"
                    >
                      <div className="flex-1">
                        <span className="font-medium text-gray-900">
                          {item.question}
                        </span>
                        <div className="mt-1">
                          <span className="inline-block rounded-full bg-primary-100 px-2 py-1 text-xs font-medium text-primary-800">
                            {item.category}
                          </span>
                        </div>
                      </div>
                      {openFAQ === item.id ? (
                        <ChevronUp className="ml-4 h-5 w-5 text-gray-500" />
                      ) : (
                        <ChevronDown className="ml-4 h-5 w-5 text-gray-500" />
                      )}
                    </button>
                    {openFAQ === item.id && (
                      <div className="border-t px-6 pb-6 pt-4">
                        <p className="text-gray-600">{item.answer}</p>
                      </div>
                    )}
                  </div>
                ))}
              </div>
            ) : (
              <div className="py-12 text-center">
                <HelpCircle className="mx-auto mb-4 h-12 w-12 text-gray-400" />
                <h3 className="mb-2 text-lg font-medium text-gray-900">
                  Nenašli jsme odpověď
                </h3>
                <p className="mb-6 text-gray-600">
                  Zkuste jiné hledané výrazy nebo se podívejte do jiné kategorie
                </p>
                <button
                  onClick={() => {
                    setSearchQuery('');
                    setSelectedCategory('Všechny');
                  }}
                  className="text-primary-600 hover:text-primary-500"
                >
                  Zobrazit všechny otázky
                </button>
              </div>
            )}
          </div>
        </section>

        {/* Contact CTA */}
        <section className="bg-white py-16">
          <div className="mx-auto max-w-7xl px-4 sm:px-6 lg:px-8">
            <div className="text-center">
              <h2 className="mb-4 text-2xl font-bold text-gray-900">
                Nenašli jste odpověď na svou otázku?
              </h2>
              <p className="mb-8 text-gray-600">
                Náš tým podpory je připraven vám pomoci s jakýmkoliv dotazem
              </p>
              <div className="flex flex-col justify-center gap-4 sm:flex-row">
                <a href="/kontakt" className="btn btn-primary">
                  Kontaktovat podporu
                </a>
                <a href="/napoveda" className="btn btn-secondary">
                  Nápověda a průvodce
                </a>
              </div>
            </div>
          </div>
        </section>
      </div>
    </Layout>
  );
}
