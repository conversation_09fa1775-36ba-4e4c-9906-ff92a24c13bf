'use client';

import { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import { Calendar, Clock, User, Package } from 'lucide-react';
import toast from 'react-hot-toast';
import { useAuth } from '@/hooks/useAuth';
import {
  useBookings,
  useApproveBooking,
  useRejectBooking,
  useCompleteBooking,
  useCancelBooking,
} from '@/hooks/useBookings';
import { ROUTES } from '@/lib/routes';
import Layout from '@/components/layout/Layout';

export default function PujcovaniPage() {
  const router = useRouter();
  const { isAuthenticated, isLoadingUser } = useAuth();
  const [activeTab, setActiveTab] = useState<'outgoing' | 'incoming'>(
    'outgoing'
  );
  const [actionLoading, setActionLoading] = useState<number | null>(null);

  // Použij React Query hooks místo přímého volání API
  const { data: bookings = [], isLoading: loading } = useBookings({
    type: activeTab,
  });
  const approveBookingMutation = useApproveBooking();
  const rejectBookingMutation = useRejectBooking();
  const completeBookingMutation = useCompleteBooking();
  const cancelBookingMutation = useCancelBooking();

  // Přesměruj nepřihlášené uživatele
  useEffect(() => {
    if (!isLoadingUser && !isAuthenticated) {
      router.push(ROUTES.AUTH.LOGIN);
      return;
    }
  }, [isAuthenticated, isLoadingUser, router]);

  const handleTabChange = (tab: 'outgoing' | 'incoming') => {
    if (tab !== activeTab) {
      setActiveTab(tab);
    }
  };

  const handleApprove = async (bookingId: number) => {
    setActionLoading(bookingId);
    try {
      await approveBookingMutation.mutateAsync(bookingId);
      toast.success('Rezervace byla schválena');
    } catch (error: any) {
      toast.error(
        error.response?.data?.message || 'Chyba při schvalování rezervace'
      );
    } finally {
      setActionLoading(null);
    }
  };

  const handleReject = async (bookingId: number) => {
    const reason = prompt('Zadejte důvod zamítnutí:');
    if (!reason) return;

    setActionLoading(bookingId);
    try {
      await rejectBookingMutation.mutateAsync({ id: bookingId, reason });
      toast.success('Rezervace byla zamítnuta');
    } catch (error: any) {
      toast.error(
        error.response?.data?.message || 'Chyba při zamítání rezervace'
      );
    } finally {
      setActionLoading(null);
    }
  };

  const handleComplete = async (bookingId: number) => {
    setActionLoading(bookingId);
    try {
      await completeBookingMutation.mutateAsync(bookingId);
      toast.success('Rezervace byla dokončena');
    } catch (error: any) {
      toast.error(
        error.response?.data?.message || 'Chyba při dokončování rezervace'
      );
    } finally {
      setActionLoading(null);
    }
  };

  const handleCancel = async (bookingId: number) => {
    if (!confirm('Opravdu chcete zrušit tuto rezervaci?')) return;

    setActionLoading(bookingId);
    try {
      await cancelBookingMutation.mutateAsync(bookingId);
      toast.success('Rezervace byla zrušena');
    } catch (error: any) {
      toast.error(
        error.response?.data?.message || 'Chyba při rušení rezervace'
      );
    } finally {
      setActionLoading(null);
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'pending':
        return 'bg-yellow-100 text-yellow-800';
      case 'approved':
        return 'bg-green-100 text-green-800';
      case 'rejected':
        return 'bg-red-100 text-red-800';
      case 'completed':
        return 'bg-blue-100 text-blue-800';
      case 'cancelled':
        return 'bg-gray-100 text-gray-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  const getStatusText = (status: string) => {
    switch (status) {
      case 'pending':
        return 'Čeká na schválení';
      case 'approved':
        return 'Schváleno';
      case 'rejected':
        return 'Zamítnuto';
      case 'completed':
        return 'Dokončeno';
      case 'cancelled':
        return 'Zrušeno';
      default:
        return status;
    }
  };

  // Zobrazíme loading během ověřování uživatele
  if (isLoadingUser) {
    return (
      <Layout>
        <div className="flex min-h-screen items-center justify-center bg-gray-50">
          <div className="text-center">
            <div className="mx-auto h-32 w-32 animate-spin rounded-full border-b-2 border-primary-600"></div>
            <p className="mt-4 text-gray-600">Načítám...</p>
          </div>
        </div>
      </Layout>
    );
  }

  if (!isAuthenticated) {
    return null;
  }

  if (loading) {
    return (
      <Layout>
        <div className="flex min-h-screen items-center justify-center bg-gray-50">
          <div className="text-center">
            <div className="mx-auto h-32 w-32 animate-spin rounded-full border-b-2 border-primary-600"></div>
            <p className="mt-4 text-gray-600">Načítám půjčování...</p>
          </div>
        </div>
      </Layout>
    );
  }

  return (
    <Layout>
      <div className="min-h-screen bg-gray-50">
        <div className="mx-auto max-w-7xl px-4 py-8 sm:px-6 lg:px-8">
          <h1 className="mb-8 text-3xl font-bold text-gray-900">Půjčování</h1>

          {/* Tabs */}
          <div className="mb-8 rounded-lg bg-white shadow-sm">
            <div className="border-b border-gray-200">
              <nav className="-mb-px flex">
                <button
                  onClick={() => handleTabChange('outgoing')}
                  disabled={loading}
                  className={`border-b-2 px-6 py-4 text-sm font-medium disabled:opacity-50 ${
                    activeTab === 'outgoing'
                      ? 'border-primary-500 text-primary-600'
                      : 'border-transparent text-gray-500 hover:border-gray-300 hover:text-gray-700'
                  }`}
                >
                  Moje žádosti
                </button>
                <button
                  onClick={() => handleTabChange('incoming')}
                  disabled={loading}
                  className={`border-b-2 px-6 py-4 text-sm font-medium disabled:opacity-50 ${
                    activeTab === 'incoming'
                      ? 'border-primary-500 text-primary-600'
                      : 'border-transparent text-gray-500 hover:border-gray-300 hover:text-gray-700'
                  }`}
                >
                  Žádosti o moje věci
                </button>
              </nav>
            </div>

            <div className="p-6">
              {loading ? (
                <div className="py-12 text-center">
                  <div className="mx-auto h-12 w-12 animate-spin rounded-full border-b-2 border-primary-600"></div>
                  <p className="mt-4 text-gray-600">Načítám data...</p>
                </div>
              ) : bookings.length > 0 ? (
                <div className="space-y-4">
                  {bookings.map((booking) => (
                    <div
                      key={booking.id}
                      className="rounded-lg border border-gray-200 p-4 transition-shadow hover:shadow-md"
                    >
                      <div className="flex items-start justify-between">
                        <div className="flex-1">
                          <div className="mb-2 flex items-center space-x-3">
                            <Package className="h-5 w-5 text-gray-400" />
                            <h3 className="text-lg font-medium text-gray-900">
                              {booking.item?.title}
                            </h3>
                            <span
                              className={`rounded-full px-2 py-1 text-xs font-medium ${getStatusColor(booking.status)}`}
                            >
                              {getStatusText(booking.status)}
                            </span>
                          </div>

                          <div className="grid grid-cols-1 gap-4 text-sm text-gray-600 md:grid-cols-3">
                            <div className="flex items-center space-x-2">
                              <Calendar className="h-4 w-4" />
                              <span>
                                {new Date(booking.startDate).toLocaleDateString(
                                  'cs-CZ'
                                )}{' '}
                                -{' '}
                                {new Date(booking.endDate).toLocaleDateString(
                                  'cs-CZ'
                                )}
                              </span>
                            </div>

                            <div className="flex items-center space-x-2">
                              <User className="h-4 w-4" />
                              <span>
                                {activeTab === 'outgoing'
                                  ? `Vlastník: ${booking.item?.owner?.firstName} ${booking.item?.owner?.lastName}`
                                  : `Žadatel: ${booking.borrower?.firstName} ${booking.borrower?.lastName}`}
                              </span>
                            </div>

                            <div className="flex items-center space-x-2">
                              <Clock className="h-4 w-4" />
                              <span>
                                Vytvořeno:{' '}
                                {new Date(booking.createdAt).toLocaleDateString(
                                  'cs-CZ'
                                )}
                              </span>
                            </div>
                          </div>

                          {booking.message && (
                            <div className="mt-3 rounded-md bg-gray-50 p-3">
                              <p className="text-sm text-gray-700">
                                <strong>Zpráva:</strong> {booking.message}
                              </p>
                            </div>
                          )}
                        </div>

                        <div className="ml-4 flex flex-col space-y-2">
                          {activeTab === 'incoming' &&
                            booking.status === 'pending' && (
                              <>
                                <button
                                  onClick={() => handleApprove(booking.id)}
                                  disabled={actionLoading === booking.id}
                                  className="rounded bg-green-600 px-3 py-1 text-sm text-white hover:bg-green-700 disabled:opacity-50"
                                >
                                  {actionLoading === booking.id
                                    ? 'Schvaluji...'
                                    : 'Schválit'}
                                </button>
                                <button
                                  onClick={() => handleReject(booking.id)}
                                  disabled={actionLoading === booking.id}
                                  className="rounded bg-red-600 px-3 py-1 text-sm text-white hover:bg-red-700 disabled:opacity-50"
                                >
                                  {actionLoading === booking.id
                                    ? 'Zamítám...'
                                    : 'Zamítnout'}
                                </button>
                              </>
                            )}

                          {booking.status === 'approved' && (
                            <button
                              onClick={() => handleComplete(booking.id)}
                              disabled={actionLoading === booking.id}
                              className="rounded bg-blue-600 px-3 py-1 text-sm text-white hover:bg-blue-700 disabled:opacity-50"
                            >
                              {actionLoading === booking.id
                                ? 'Dokončuji...'
                                : 'Označit jako dokončené'}
                            </button>
                          )}

                          {activeTab === 'outgoing' &&
                            ['pending', 'approved'].includes(
                              booking.status
                            ) && (
                              <button
                                onClick={() => handleCancel(booking.id)}
                                disabled={actionLoading === booking.id}
                                className="rounded bg-gray-600 px-3 py-1 text-sm text-white hover:bg-gray-700 disabled:opacity-50"
                              >
                                {actionLoading === booking.id
                                  ? 'Ruším...'
                                  : 'Zrušit'}
                              </button>
                            )}
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              ) : (
                <div className="py-12 text-center">
                  <Package className="mx-auto mb-4 h-16 w-16 text-gray-400" />
                  <h3 className="mb-2 text-lg font-medium text-gray-900">
                    {activeTab === 'outgoing'
                      ? 'Žádné žádosti o půjčení'
                      : 'Žádné žádosti o vaše věci'}
                  </h3>
                  <p className="text-gray-600">
                    {activeTab === 'outgoing'
                      ? 'Zatím jste nepožádali o půjčení žádného předmětu.'
                      : 'Zatím nikdo nepožádal o půjčení vašich předmětů.'}
                  </p>
                </div>
              )}
            </div>
          </div>
        </div>
      </div>
    </Layout>
  );
}
