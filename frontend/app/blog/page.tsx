'use client';

import { useState } from 'react';
import Layout from '@/components/layout/Layout';
import {
  Calendar,
  User,
  ArrowRight,
  Search,
  Tag,
  Clock,
  TrendingUp,
  MessageCircle,
  Heart,
} from 'lucide-react';

interface BlogPost {
  id: string;
  title: string;
  excerpt: string;
  content: string;
  author: string;
  publishedAt: string;
  readTime: number;
  category: string;
  tags: string[];
  featured?: boolean;
  views: number;
  likes: number;
  comments: number;
}

const blogPosts: BlogPost[] = [
  {
    id: '1',
    title: 'Jak začít s půjčováním věcí: Kompletní průvodce pro začátečníky',
    excerpt:
      'Objevte svět sdílené ekonomiky a naučte se, jak bezpečně půjčovat a vypůjčovat si věci od sousedů. Tipy, triky a nejlepší praktiky.',
    content: '',
    author: '<PERSON>',
    publishedAt: '2025-01-15',
    readTime: 8,
    category: 'Pr<PERSON><PERSON><PERSON><PERSON>',
    tags: ['za<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>', 'tipy', 'bezpečnost'],
    featured: true,
    views: 1250,
    likes: 89,
    comments: 23,
  },
  {
    id: '2',
    title: 'Top 10 nejpůjčovanějších věcí v roce 2024',
    excerpt:
      'Podívejte se na statistiky a zjistěte, které věci si lidé nejčastěji půjčují. Možná vás některé překvapí!',
    content: '',
    author: 'Tomáš Svoboda',
    publishedAt: '2025-01-10',
    readTime: 5,
    category: 'Statistiky',
    tags: ['statistiky', 'trendy', '2024'],
    views: 890,
    likes: 67,
    comments: 15,
  },
  {
    id: '3',
    title: 'Udržitelnost a sdílená ekonomika: Jak pomáháme životnímu prostředí',
    excerpt:
      'Zjistěte, jak půjčování věcí přispívá k ochraně životního prostředí a snižování uhlíkové stopy.',
    content: '',
    author: 'Petra Zelená',
    publishedAt: '2025-01-08',
    readTime: 6,
    category: 'Udržitelnost',
    tags: ['ekologie', 'udržitelnost', 'životní prostředí'],
    featured: true,
    views: 720,
    likes: 95,
    comments: 31,
  },
  {
    id: '4',
    title: 'Bezpečnostní tipy: Jak se vyhnout problémům při půjčování',
    excerpt:
      'Praktické rady pro bezpečné půjčování. Co dělat před, během a po půjčení, aby vše proběhlo hladce.',
    content: '',
    author: 'Martin Bezpečný',
    publishedAt: '2025-01-05',
    readTime: 7,
    category: 'Bezpečnost',
    tags: ['bezpečnost', 'tipy', 'prevence'],
    views: 650,
    likes: 78,
    comments: 19,
  },
  {
    id: '5',
    title: 'Příběhy uživatelů: Jak Pujčovna změnila náš život',
    excerpt:
      'Inspirativní příběhy skutečných uživatelů, kteří díky sdílení věcí ušetřili peníze a našli nové přátele.',
    content: '',
    author: 'Lucie Příběhová',
    publishedAt: '2025-01-03',
    readTime: 10,
    category: 'Příběhy',
    tags: ['příběhy', 'komunita', 'inspirace'],
    views: 980,
    likes: 124,
    comments: 45,
  },
  {
    id: '6',
    title: 'Novinky v aplikaci: Co nového jsme přidali v lednu 2025',
    excerpt:
      'Přehled nejnovějších funkcí a vylepšení, které jsme přidali do platformy na základě vašich připomínek.',
    content: '',
    author: 'Vývojový tým',
    publishedAt: '2025-01-01',
    readTime: 4,
    category: 'Novinky',
    tags: ['novinky', 'funkce', 'aktualizace'],
    views: 540,
    likes: 56,
    comments: 12,
  },
];

const categories = [
  'Všechny',
  'Průvodce',
  'Statistiky',
  'Udržitelnost',
  'Bezpečnost',
  'Příběhy',
  'Novinky',
];

export default function BlogPage() {
  const [searchQuery, setSearchQuery] = useState('');
  const [selectedCategory, setSelectedCategory] = useState('Všechny');

  const filteredPosts = blogPosts.filter((post) => {
    const matchesSearch =
      post.title.toLowerCase().includes(searchQuery.toLowerCase()) ||
      post.excerpt.toLowerCase().includes(searchQuery.toLowerCase()) ||
      post.tags.some((tag) =>
        tag.toLowerCase().includes(searchQuery.toLowerCase())
      );
    const matchesCategory =
      selectedCategory === 'Všechny' || post.category === selectedCategory;
    return matchesSearch && matchesCategory;
  });

  const featuredPosts = blogPosts.filter((post) => post.featured);
  const regularPosts = filteredPosts.filter((post) => !post.featured);

  const formatDate = (dateString: string) => {
    const date = new Date(dateString);
    return date.toLocaleDateString('cs-CZ', {
      year: 'numeric',
      month: 'long',
      day: 'numeric',
    });
  };

  return (
    <Layout>
      <div className="bg-gray-50">
        {/* Hero Section */}
        <section className="bg-primary-600 py-16 text-white">
          <div className="mx-auto max-w-7xl px-4 sm:px-6 lg:px-8">
            <div className="text-center">
              <h1 className="mb-6 text-4xl font-bold md:text-5xl">
                Blog Pujčovna
              </h1>
              <p className="mx-auto max-w-3xl text-xl text-primary-100">
                Tipy, novinky a příběhy ze světa sdílené ekonomiky. Inspirujte
                se a dozvězte se více o komunitním půjčování.
              </p>
            </div>
          </div>
        </section>

        {/* Search and Filter */}
        <section className="py-8">
          <div className="mx-auto max-w-4xl px-4 sm:px-6 lg:px-8">
            {/* Search */}
            <div className="mb-6">
              <div className="relative">
                <Search className="absolute left-3 top-1/2 h-5 w-5 -translate-y-1/2 text-gray-400" />
                <input
                  type="text"
                  placeholder="Hledejte v článcích..."
                  value={searchQuery}
                  onChange={(e) => setSearchQuery(e.target.value)}
                  className="w-full rounded-lg border border-gray-300 py-3 pl-10 pr-4 focus:border-primary-500 focus:outline-none focus:ring-1 focus:ring-primary-500"
                />
              </div>
            </div>

            {/* Categories */}
            <div className="flex flex-wrap gap-2">
              {categories.map((category) => (
                <button
                  key={category}
                  onClick={() => setSelectedCategory(category)}
                  className={`rounded-full px-4 py-2 text-sm font-medium transition-colors ${
                    selectedCategory === category
                      ? 'bg-primary-600 text-white'
                      : 'bg-white text-gray-700 hover:bg-gray-100'
                  }`}
                >
                  {category}
                </button>
              ))}
            </div>
          </div>
        </section>

        {/* Featured Posts */}
        {!searchQuery && selectedCategory === 'Všechny' && (
          <section className="py-8">
            <div className="mx-auto max-w-7xl px-4 sm:px-6 lg:px-8">
              <div className="mb-8 flex items-center">
                <TrendingUp className="mr-2 h-6 w-6 text-orange-600" />
                <h2 className="text-2xl font-bold text-gray-900">
                  Doporučené články
                </h2>
              </div>

              <div className="grid grid-cols-1 gap-8 lg:grid-cols-2">
                {featuredPosts.map((post) => (
                  <article
                    key={post.id}
                    className="group overflow-hidden rounded-lg bg-white shadow-sm hover:shadow-md"
                  >
                    <div className="p-6">
                      <div className="mb-4 flex items-center justify-between">
                        <span className="rounded-full bg-orange-100 px-3 py-1 text-sm font-medium text-orange-800">
                          Doporučené
                        </span>
                        <span className="rounded-full bg-primary-100 px-3 py-1 text-sm font-medium text-primary-800">
                          {post.category}
                        </span>
                      </div>

                      <h3 className="mb-3 text-xl font-bold text-gray-900 group-hover:text-primary-600">
                        {post.title}
                      </h3>

                      <p className="mb-4 text-gray-600">{post.excerpt}</p>

                      <div className="mb-4 flex flex-wrap gap-2">
                        {post.tags.map((tag) => (
                          <span
                            key={tag}
                            className="inline-flex items-center rounded-full bg-gray-100 px-2 py-1 text-xs text-gray-600"
                          >
                            <Tag className="mr-1 h-3 w-3" />
                            {tag}
                          </span>
                        ))}
                      </div>

                      <div className="flex items-center justify-between text-sm text-gray-500">
                        <div className="flex items-center space-x-4">
                          <div className="flex items-center">
                            <User className="mr-1 h-4 w-4" />
                            {post.author}
                          </div>
                          <div className="flex items-center">
                            <Calendar className="mr-1 h-4 w-4" />
                            {formatDate(post.publishedAt)}
                          </div>
                          <div className="flex items-center">
                            <Clock className="mr-1 h-4 w-4" />
                            {post.readTime} min čtení
                          </div>
                        </div>
                      </div>

                      <div className="mt-4 flex items-center justify-between">
                        <div className="flex items-center space-x-4 text-sm text-gray-500">
                          <div className="flex items-center">
                            <Heart className="mr-1 h-4 w-4" />
                            {post.likes}
                          </div>
                          <div className="flex items-center">
                            <MessageCircle className="mr-1 h-4 w-4" />
                            {post.comments}
                          </div>
                        </div>

                        <button className="flex items-center text-primary-600 hover:text-primary-700">
                          <span className="mr-1">Číst více</span>
                          <ArrowRight className="h-4 w-4" />
                        </button>
                      </div>
                    </div>
                  </article>
                ))}
              </div>
            </div>
          </section>
        )}

        {/* Regular Posts */}
        <section className="pb-16">
          <div className="mx-auto max-w-7xl px-4 sm:px-6 lg:px-8">
            <h2 className="mb-8 text-2xl font-bold text-gray-900">
              {searchQuery || selectedCategory !== 'Všechny'
                ? 'Výsledky'
                : 'Všechny články'}
            </h2>

            {filteredPosts.length > 0 ? (
              <div className="grid grid-cols-1 gap-8 md:grid-cols-2 lg:grid-cols-3">
                {(searchQuery || selectedCategory !== 'Všechny'
                  ? filteredPosts
                  : regularPosts
                ).map((post) => (
                  <article
                    key={post.id}
                    className="group overflow-hidden rounded-lg bg-white shadow-sm hover:shadow-md"
                  >
                    <div className="p-6">
                      <div className="mb-4">
                        <span className="rounded-full bg-primary-100 px-3 py-1 text-sm font-medium text-primary-800">
                          {post.category}
                        </span>
                      </div>

                      <h3 className="mb-3 text-lg font-semibold text-gray-900 group-hover:text-primary-600">
                        {post.title}
                      </h3>

                      <p className="mb-4 text-sm text-gray-600">
                        {post.excerpt}
                      </p>

                      <div className="mb-4 flex flex-wrap gap-1">
                        {post.tags.slice(0, 2).map((tag) => (
                          <span
                            key={tag}
                            className="inline-flex items-center rounded-full bg-gray-100 px-2 py-1 text-xs text-gray-600"
                          >
                            <Tag className="mr-1 h-3 w-3" />
                            {tag}
                          </span>
                        ))}
                        {post.tags.length > 2 && (
                          <span className="inline-flex items-center rounded-full bg-gray-100 px-2 py-1 text-xs text-gray-600">
                            +{post.tags.length - 2}
                          </span>
                        )}
                      </div>

                      <div className="mb-4 flex items-center text-xs text-gray-500">
                        <User className="mr-1 h-3 w-3" />
                        <span className="mr-3">{post.author}</span>
                        <Calendar className="mr-1 h-3 w-3" />
                        <span className="mr-3">
                          {formatDate(post.publishedAt)}
                        </span>
                        <Clock className="mr-1 h-3 w-3" />
                        <span>{post.readTime} min</span>
                      </div>

                      <div className="flex items-center justify-between">
                        <div className="flex items-center space-x-3 text-xs text-gray-500">
                          <div className="flex items-center">
                            <Heart className="mr-1 h-3 w-3" />
                            {post.likes}
                          </div>
                          <div className="flex items-center">
                            <MessageCircle className="mr-1 h-3 w-3" />
                            {post.comments}
                          </div>
                        </div>

                        <button className="flex items-center text-sm text-primary-600 hover:text-primary-700">
                          <span className="mr-1">Číst</span>
                          <ArrowRight className="h-3 w-3" />
                        </button>
                      </div>
                    </div>
                  </article>
                ))}
              </div>
            ) : (
              <div className="py-12 text-center">
                <Search className="mx-auto mb-4 h-12 w-12 text-gray-400" />
                <h3 className="mb-2 text-lg font-medium text-gray-900">
                  Nenašli jsme žádné články
                </h3>
                <p className="text-gray-600">
                  Zkuste jiné hledané výrazy nebo procházejte všechny kategorie
                </p>
                <button
                  onClick={() => {
                    setSearchQuery('');
                    setSelectedCategory('Všechny');
                  }}
                  className="mt-4 text-primary-600 hover:text-primary-500"
                >
                  Zobrazit všechny články
                </button>
              </div>
            )}
          </div>
        </section>

        {/* Newsletter CTA */}
        <section className="bg-white py-16">
          <div className="mx-auto max-w-7xl px-4 sm:px-6 lg:px-8">
            <div className="text-center">
              <h2 className="mb-4 text-2xl font-bold text-gray-900">
                Nezmeškejte žádný článek
              </h2>
              <p className="mb-8 text-gray-600">
                Přihlaste se k odběru našeho newsletteru a dostávejte nejnovější
                články přímo do emailu
              </p>
              <div className="mx-auto max-w-md">
                <div className="flex gap-2">
                  <input
                    type="email"
                    placeholder="Váš email"
                    className="flex-1 rounded-lg border border-gray-300 px-4 py-2 focus:border-primary-500 focus:outline-none focus:ring-1 focus:ring-primary-500"
                  />
                  <button className="btn btn-primary">Přihlásit se</button>
                </div>
              </div>
            </div>
          </div>
        </section>
      </div>
    </Layout>
  );
}
