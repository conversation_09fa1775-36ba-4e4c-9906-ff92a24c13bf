'use client';

import { useAuth } from '@/hooks/useAuth';
import { useItems } from '@/hooks/useItems';
import { useMyItems } from '@/hooks/useMyItems';
import { useDashboardStats, useUserActivities } from '@/hooks/useDashboard';
import { useBookings } from '@/hooks/useBookings';
import Layout from '@/components/layout/Layout';
import Card from '@/components/ui/Card';
import Button from '@/components/ui/Button';
import LoadingLink from '@/components/ui/LoadingLink';
import {
  Plus,
  Package,
  Calendar,
  Star,
  TrendingUp,
  Edit,
  Eye,
} from 'lucide-react';
import { ROUTES } from '@/lib/routes';
import { getItemPrice } from '@/lib/price-utils';

export default function DashboardPage() {
  const { user, isLoadingUser } = useAuth();
  const { data: itemsData, isLoading: isLoadingItems } = useItems({
    limit: 5,
    excludeOwn: true,
  });
  const { data: myItemsData, isLoading: isLoadingMyItems } = useMyItems(3);
  const {
    data: stats,
    isLoading: isLoadingStats,
    error: statsError,
  } = useDashboardStats();
  const {
    data: activities,
    isLoading: isLoadingActivities,
    error: activitiesError,
  } = useUserActivities();
  const { data: recentBookings, isLoading: isLoadingBookings } = useBookings({
    limit: 5,
  });

  if (isLoadingUser) {
    return (
      <Layout>
        <div className="flex min-h-screen items-center justify-center">
          <div className="h-32 w-32 animate-spin rounded-full border-b-2 border-primary-600"></div>
        </div>
      </Layout>
    );
  }

  if (!user) {
    return (
      <Layout>
        <div className="flex min-h-screen items-center justify-center">
          <div className="text-center">
            <h1 className="mb-4 text-2xl font-bold text-gray-900">
              Přístup odepřen
            </h1>
            <p className="mb-4 text-gray-600">
              Pro přístup k dashboardu se musíte přihlásit.
            </p>
            <LoadingLink href={ROUTES.AUTH.LOGIN}>
              <Button>Přihlásit se</Button>
            </LoadingLink>
          </div>
        </div>
      </Layout>
    );
  }

  return (
    <Layout>
      <div className="mx-auto max-w-7xl px-4 py-8 sm:px-6 lg:px-8">
        {/* Welcome Section */}
        <div className="mb-8">
          <h1 className="text-3xl font-bold text-gray-900">
            Vítejte zpět, {user.firstName}!
          </h1>
          <p className="mt-2 text-gray-600">
            Zde je přehled vaší aktivity na platformě
          </p>
        </div>

        {/* Stats Cards */}
        <div className="mb-8 grid grid-cols-1 gap-6 md:grid-cols-2 lg:grid-cols-4">
          <Card>
            <div className="flex items-center">
              <div className="flex-shrink-0">
                <Package className="h-8 w-8 text-primary-600" />
              </div>
              <div className="ml-4">
                <p className="text-sm font-medium text-gray-500">Moje věci</p>
                <p className="text-2xl font-semibold text-gray-900">
                  {isLoadingStats
                    ? '...'
                    : statsError
                      ? '0'
                      : stats?.itemsCount || 0}
                </p>
              </div>
            </div>
          </Card>

          <Card>
            <div className="flex items-center">
              <div className="flex-shrink-0">
                <Calendar className="h-8 w-8 text-green-600" />
              </div>
              <div className="ml-4">
                <p className="text-sm font-medium text-gray-500">
                  Aktivní rezervace
                </p>
                <p className="text-2xl font-semibold text-gray-900">
                  {isLoadingStats
                    ? '...'
                    : statsError
                      ? '0'
                      : stats?.activeBookingsCount || 0}
                </p>
              </div>
            </div>
          </Card>

          <Card>
            <div className="flex items-center">
              <div className="flex-shrink-0">
                <Star className="h-8 w-8 text-yellow-600" />
              </div>
              <div className="ml-4">
                <p className="text-sm font-medium text-gray-500">Hodnocení</p>
                <p className="text-2xl font-semibold text-gray-900">
                  {user.rating ? Number(user.rating).toFixed(1) : 'N/A'}
                </p>
              </div>
            </div>
          </Card>

          <Card>
            <div className="flex items-center">
              <div className="flex-shrink-0">
                <TrendingUp className="h-8 w-8 text-blue-600" />
              </div>
              <div className="ml-4">
                <p className="text-sm font-medium text-gray-500">
                  Čekající rezervace
                </p>
                <p className="text-2xl font-semibold text-gray-900">
                  {isLoadingStats
                    ? '...'
                    : statsError
                      ? '0'
                      : stats?.pendingBookingsCount || 0}
                </p>
              </div>
            </div>
          </Card>
        </div>

        <div className="grid grid-cols-1 gap-8 lg:grid-cols-2">
          {/* Quick Actions */}
          <Card>
            <h2 className="mb-4 text-lg font-semibold text-gray-900">
              Rychlé akce
            </h2>
            <div className="space-y-3">
              <LoadingLink href={ROUTES.ITEMS.CREATE}>
                <Button className="w-full justify-start" variant="outline">
                  <Plus className="mr-2 h-4 w-4" />
                  Přidat novou věc
                </Button>
              </LoadingLink>
              <LoadingLink href={ROUTES.USER.BOOKINGS}>
                <Button className="w-full justify-start" variant="outline">
                  <Calendar className="mr-2 h-4 w-4" />
                  Zobrazit rezervace
                </Button>
              </LoadingLink>
              <LoadingLink href={ROUTES.USER.PROFILE}>
                <Button className="w-full justify-start" variant="outline">
                  <Star className="mr-2 h-4 w-4" />
                  Upravit profil
                </Button>
              </LoadingLink>
            </div>
          </Card>

          {/* Recent Activity */}
          <Card>
            <h2 className="mb-4 text-lg font-semibold text-gray-900">
              Nedávná aktivita
            </h2>
            {isLoadingActivities ? (
              <div className="space-y-4">
                {[...Array(3)].map((_, i) => (
                  <div
                    key={i}
                    className="flex animate-pulse items-center space-x-3"
                  >
                    <div className="h-2 w-2 rounded-full bg-gray-300"></div>
                    <div className="flex-1">
                      <div className="mb-1 h-4 rounded bg-gray-200"></div>
                      <div className="h-3 w-1/3 rounded bg-gray-200"></div>
                    </div>
                  </div>
                ))}
              </div>
            ) : (
              <div className="space-y-4">
                {activitiesError ? (
                  <div className="py-4 text-center">
                    <p className="text-sm text-gray-500">
                      Nepodařilo se načíst aktivity
                    </p>
                  </div>
                ) : activities &&
                  Array.isArray(activities) &&
                  activities.length > 0 ? (
                  activities.slice(0, 5).map((activity, index) => {
                    const getStatusColor = (status: string) => {
                      switch (status) {
                        case 'pending':
                          return 'bg-yellow-500';
                        case 'approved':
                          return 'bg-green-500';
                        case 'active':
                          return 'bg-blue-500';
                        case 'completed':
                          return 'bg-gray-500';
                        case 'cancelled':
                          return 'bg-red-500';
                        default:
                          return 'bg-gray-400';
                      }
                    };

                    const formatDate = (dateString: string) => {
                      const date = new Date(dateString);
                      const now = new Date();
                      const diffMs = now.getTime() - date.getTime();
                      const diffHours = Math.floor(diffMs / (1000 * 60 * 60));
                      const diffDays = Math.floor(diffHours / 24);

                      if (diffHours < 1) return 'před chvílí';
                      if (diffHours < 24) return `před ${diffHours} hodinami`;
                      if (diffDays === 1) return 'včera';
                      if (diffDays < 7) return `před ${diffDays} dny`;
                      return date.toLocaleDateString('cs-CZ');
                    };

                    return (
                      <div key={index} className="flex items-center space-x-3">
                        <div
                          className={`h-2 w-2 rounded-full ${getStatusColor(activity.status)}`}
                        ></div>
                        <div className="flex-1">
                          <p className="text-sm text-gray-900">
                            {activity.message}
                          </p>
                          <p className="text-xs text-gray-500">
                            {formatDate(activity.date)}
                          </p>
                        </div>
                      </div>
                    );
                  })
                ) : (
                  <div className="py-4 text-center">
                    <p className="text-sm text-gray-500">
                      Zatím žádná aktivita
                    </p>
                  </div>
                )}
              </div>
            )}
          </Card>
        </div>

        {/* My Items */}
        <div className="mt-8">
          <div className="mb-6 flex items-center justify-between">
            <h2 className="text-lg font-semibold text-gray-900">
              Moje předměty
            </h2>
            <LoadingLink href={ROUTES.USER.MY_ITEMS}>
              <Button variant="outline" size="sm">
                Zobrazit všechny
              </Button>
            </LoadingLink>
          </div>

          {isLoadingMyItems ? (
            <div className="grid grid-cols-1 gap-6 md:grid-cols-2 lg:grid-cols-3">
              {[...Array(3)].map((_, i) => (
                <Card key={i} className="animate-pulse">
                  <div className="mb-4 h-48 rounded-lg bg-gray-200"></div>
                  <div className="mb-2 h-4 rounded bg-gray-200"></div>
                  <div className="h-4 w-2/3 rounded bg-gray-200"></div>
                </Card>
              ))}
            </div>
          ) : (
            <div className="grid grid-cols-1 gap-6 md:grid-cols-2 lg:grid-cols-3">
              {myItemsData?.data && myItemsData.data.length > 0 ? (
                myItemsData.data.map((item) => (
                  <Card
                    key={item.id}
                    className="transition-shadow hover:shadow-md"
                  >
                    <div className="aspect-w-16 aspect-h-9 mb-4">
                      <div className="flex h-48 w-full items-center justify-center rounded-lg bg-gray-200">
                        <Package className="h-12 w-12 text-gray-400" />
                      </div>
                    </div>
                    <h3 className="mb-2 font-semibold text-gray-900">
                      {item.title}
                    </h3>
                    <p className="mb-2 text-sm text-gray-600">
                      {item.description.substring(0, 100)}...
                    </p>
                    <div className="mb-3 flex items-center justify-between">
                      <span className="text-lg font-bold text-primary-600">
                        {getItemPrice(item) || 'N/A'} Kč/den
                      </span>
                      <span
                        className={`rounded-full px-2 py-1 text-xs ${
                          item.isAvailable
                            ? 'bg-green-100 text-green-800'
                            : 'bg-red-100 text-red-800'
                        }`}
                      >
                        {item.isAvailable ? 'Dostupný' : 'Nedostupný'}
                      </span>
                    </div>
                    <div className="flex space-x-2">
                      <LoadingLink
                        href={ROUTES.ITEMS.DETAIL(item.id)}
                        className="flex-1"
                      >
                        <Button variant="outline" size="sm" className="w-full">
                          <Eye className="mr-1 h-4 w-4" />
                          Zobrazit
                        </Button>
                      </LoadingLink>
                      <LoadingLink
                        href={ROUTES.ITEMS.EDIT(item.id)}
                        className="flex-1"
                      >
                        <Button variant="outline" size="sm" className="w-full">
                          <Edit className="mr-1 h-4 w-4" />
                          Upravit
                        </Button>
                      </LoadingLink>
                    </div>
                  </Card>
                ))
              ) : (
                <div className="col-span-full py-8 text-center">
                  <Package className="mx-auto mb-4 h-12 w-12 text-gray-400" />
                  <p className="mb-4 text-gray-500">
                    Zatím nemáte žádné předměty
                  </p>
                  <LoadingLink href={ROUTES.ITEMS.CREATE}>
                    <Button>
                      <Plus className="mr-2 h-4 w-4" />
                      Přidat první předmět
                    </Button>
                  </LoadingLink>
                </div>
              )}
            </div>
          )}
        </div>

        {/* Recent Items */}
        <div className="mt-8">
          <div className="mb-6 flex items-center justify-between">
            <h2 className="text-lg font-semibold text-gray-900">
              Nejnovější věci v okolí
            </h2>
            <LoadingLink href={ROUTES.ITEMS.LIST}>
              <Button variant="outline" size="sm">
                Zobrazit všechny
              </Button>
            </LoadingLink>
          </div>

          {isLoadingItems ? (
            <div className="grid grid-cols-1 gap-6 md:grid-cols-2 lg:grid-cols-3">
              {[...Array(3)].map((_, i) => (
                <Card key={i} className="animate-pulse">
                  <div className="mb-4 h-48 rounded-lg bg-gray-200"></div>
                  <div className="mb-2 h-4 rounded bg-gray-200"></div>
                  <div className="h-4 w-2/3 rounded bg-gray-200"></div>
                </Card>
              ))}
            </div>
          ) : (
            <div className="grid grid-cols-1 gap-6 md:grid-cols-2 lg:grid-cols-3">
              {itemsData?.data && itemsData.data.length > 0 ? (
                itemsData.data.slice(0, 3).map((item) => (
                  <LoadingLink
                    key={item.id}
                    href={ROUTES.ITEMS.DETAIL(item.id)}
                    className="block"
                  >
                    <Card className="cursor-pointer transition-shadow hover:shadow-md">
                      <div className="aspect-w-16 aspect-h-9 mb-4">
                        <div className="flex h-48 w-full items-center justify-center rounded-lg bg-gray-200">
                          <Package className="h-12 w-12 text-gray-400" />
                        </div>
                      </div>
                      <h3 className="mb-2 font-semibold text-gray-900">
                        {item.title}
                      </h3>
                      <p className="mb-2 text-sm text-gray-600">
                        {item.description.substring(0, 100)}...
                      </p>
                      <div className="flex items-center justify-between">
                        <span className="text-lg font-bold text-primary-600">
                          {getItemPrice(item) || 'N/A'} Kč/den
                        </span>
                        <span className="text-sm text-gray-500">
                          {item.owner?.address?.city || 'Neznámé místo'}
                        </span>
                      </div>
                    </Card>
                  </LoadingLink>
                ))
              ) : (
                <div className="col-span-full py-8 text-center">
                  <Package className="mx-auto mb-4 h-12 w-12 text-gray-400" />
                  <p className="text-gray-500">
                    Zatím nejsou k dispozici žádné věci
                  </p>
                </div>
              )}
            </div>
          )}
        </div>
      </div>
    </Layout>
  );
}
