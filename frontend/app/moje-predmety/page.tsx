'use client';

import { useRouter } from 'next/navigation';
import { useEffect } from 'react';
import { Plus, Edit, Trash2, Eye } from 'lucide-react';
import { useAuth } from '@/hooks/useAuth';
import { useMyItems } from '@/hooks/useMyItems';
import { useDeleteItem } from '@/hooks/useItems';
import Layout from '@/components/layout/Layout';
import { ROUTES } from '@/lib/routes';

export default function MojePredmetyPage() {
  const router = useRouter();
  const { isAuthenticated, isLoadingUser } = useAuth();

  // Použij React Query hooks místo přímého volání API
  const { data: itemsData, isLoading: isLoadingItems } = useMyItems();
  const deleteItemMutation = useDeleteItem();

  const items = itemsData?.data || [];
  const loading = isLoadingItems;

  // Přesměruj nepř<PERSON>lášené uživatele
  useEffect(() => {
    if (!isLoadingUser && !isAuthenticated) {
      router.push(ROUTES.AUTH.LOGIN);
    }
  }, [isAuthenticated, isLoadingUser, router]);

  // Zobraz loading během načítání uživatele
  if (isLoadingUser) {
    return (
      <Layout>
        <div className="flex min-h-screen items-center justify-center bg-gray-50">
          <div className="text-center">
            <div className="mx-auto h-32 w-32 animate-spin rounded-full border-b-2 border-primary-600"></div>
            <p className="mt-4 text-gray-600">Ověřuji přihlášení...</p>
          </div>
        </div>
      </Layout>
    );
  }

  const handleDelete = async (itemId: number) => {
    if (!confirm('Opravdu chcete smazat tento předmět?')) {
      return;
    }

    try {
      await deleteItemMutation.mutateAsync(itemId);
    } catch (error) {
      console.error('Chyba při mazání předmětu:', error);
    }
  };

  if (!isAuthenticated) {
    return null;
  }

  if (loading) {
    return (
      <div className="flex min-h-screen items-center justify-center bg-gray-50">
        <div className="text-center">
          <div className="mx-auto h-32 w-32 animate-spin rounded-full border-b-2 border-primary-600"></div>
          <p className="mt-4 text-gray-600">Načítám vaše předměty...</p>
        </div>
      </div>
    );
  }

  return (
    <Layout>
      <div className="min-h-screen bg-gray-50">
        <div className="mx-auto max-w-7xl px-4 py-8 sm:px-6 lg:px-8">
          <div className="mb-8 flex items-center justify-between">
            <h1 className="text-3xl font-bold text-gray-900">Moje předměty</h1>
            <button
              onClick={() => router.push(ROUTES.ITEMS.CREATE)}
              className="btn btn-primary flex items-center space-x-2"
            >
              <Plus className="h-5 w-5" />
              <span>Přidat předmět</span>
            </button>
          </div>

          {items.length > 0 ? (
            <div className="overflow-hidden rounded-lg bg-white shadow-sm">
              <div className="overflow-x-auto">
                <table className="min-w-full divide-y divide-gray-200">
                  <thead className="bg-gray-50">
                    <tr>
                      <th className="px-6 py-3 text-left text-xs font-medium uppercase tracking-wider text-gray-500">
                        Předmět
                      </th>
                      <th className="px-6 py-3 text-left text-xs font-medium uppercase tracking-wider text-gray-500">
                        Kategorie
                      </th>
                      <th className="px-6 py-3 text-left text-xs font-medium uppercase tracking-wider text-gray-500">
                        Cena/den
                      </th>
                      <th className="px-6 py-3 text-left text-xs font-medium uppercase tracking-wider text-gray-500">
                        Stav
                      </th>
                      <th className="px-6 py-3 text-left text-xs font-medium uppercase tracking-wider text-gray-500">
                        Dostupnost
                      </th>
                      <th className="px-6 py-3 text-right text-xs font-medium uppercase tracking-wider text-gray-500">
                        Akce
                      </th>
                    </tr>
                  </thead>
                  <tbody className="divide-y divide-gray-200 bg-white">
                    {items.map((item) => (
                      <tr key={item.id} className="hover:bg-gray-50">
                        <td className="whitespace-nowrap px-6 py-4">
                          <div className="flex items-center">
                            <div className="h-12 w-12 flex-shrink-0">
                              <div className="flex h-12 w-12 items-center justify-center rounded-lg bg-gray-200">
                                <span className="text-sm font-medium text-gray-500">
                                  {item.title.charAt(0).toUpperCase()}
                                </span>
                              </div>
                            </div>
                            <div className="ml-4">
                              <div className="text-sm font-medium text-gray-900">
                                {item.title}
                              </div>
                              <div className="max-w-xs truncate text-sm text-gray-500">
                                {item.description}
                              </div>
                            </div>
                          </div>
                        </td>
                        <td className="whitespace-nowrap px-6 py-4 text-sm text-gray-900">
                          {item.category?.name || 'Bez kategorie'}
                        </td>
                        <td className="whitespace-nowrap px-6 py-4 text-sm text-gray-900">
                          {item.pricePerDay} Kč
                        </td>
                        <td className="whitespace-nowrap px-6 py-4">
                          <span
                            className={`inline-flex rounded-full px-2 py-1 text-xs font-semibold ${
                              item.condition === 'excellent'
                                ? 'bg-green-100 text-green-800'
                                : item.condition === 'good'
                                  ? 'bg-blue-100 text-blue-800'
                                  : item.condition === 'fair'
                                    ? 'bg-yellow-100 text-yellow-800'
                                    : 'bg-red-100 text-red-800'
                            }`}
                          >
                            {item.condition === 'excellent'
                              ? 'Výborný'
                              : item.condition === 'good'
                                ? 'Dobrý'
                                : item.condition === 'fair'
                                  ? 'Průměrný'
                                  : 'Špatný'}
                          </span>
                        </td>
                        <td className="whitespace-nowrap px-6 py-4">
                          <span
                            className={`inline-flex rounded-full px-2 py-1 text-xs font-semibold ${
                              item.isAvailable
                                ? 'bg-green-100 text-green-800'
                                : 'bg-red-100 text-red-800'
                            }`}
                          >
                            {item.isAvailable ? 'Dostupný' : 'Nedostupný'}
                          </span>
                        </td>
                        <td className="whitespace-nowrap px-6 py-4 text-right text-sm font-medium">
                          <div className="flex justify-end space-x-2">
                            <button
                              onClick={() =>
                                router.push(ROUTES.ITEMS.DETAIL(item.id))
                              }
                              className="text-blue-600 hover:text-blue-900"
                              title="Zobrazit"
                            >
                              <Eye className="h-4 w-4" />
                            </button>
                            <button
                              onClick={() =>
                                router.push(ROUTES.ITEMS.EDIT(item.id))
                              }
                              className="text-indigo-600 hover:text-indigo-900"
                              title="Upravit"
                            >
                              <Edit className="h-4 w-4" />
                            </button>
                            <button
                              onClick={() => handleDelete(item.id)}
                              className="text-red-600 hover:text-red-900"
                              title="Smazat"
                            >
                              <Trash2 className="h-4 w-4" />
                            </button>
                          </div>
                        </td>
                      </tr>
                    ))}
                  </tbody>
                </table>
              </div>
            </div>
          ) : (
            <div className="rounded-lg bg-white p-12 text-center shadow-sm">
              <div className="mb-4 text-gray-400">
                <Plus className="mx-auto h-16 w-16" />
              </div>
              <h3 className="mb-2 text-lg font-medium text-gray-900">
                Zatím nemáte žádné předměty
              </h3>
              <p className="mb-6 text-gray-600">
                Začněte přidáváním předmětů, které chcete půjčovat ostatním.
              </p>
              <button
                onClick={() => router.push(ROUTES.ITEMS.CREATE)}
                className="btn btn-primary"
              >
                Přidat první předmět
              </button>
            </div>
          )}
        </div>
      </div>
    </Layout>
  );
}
