'use client';

import { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import Link from 'next/link';
import toast from 'react-hot-toast';
import Layout from '@/components/layout/Layout';
import { ROUTES } from '@/lib/routes';
import { apiClient } from '@/lib/api';
import { ApiError } from '@/types';

export default function ResendActivationPage() {
  const router = useRouter();
  const [isResending, setIsResending] = useState(false);
  const [emailSent, setEmailSent] = useState(false);
  const [email, setEmail] = useState<string | null>(null);

  // Načti email ze sessionStorage
  useEffect(() => {
    const storedEmail = sessionStorage.getItem('resendActivationEmail');
    if (storedEmail) {
      setEmail(storedEmail);
    } else {
      // Pokud není email v sessionStorage, přesměruj na registraci
      router.push(ROUTES.AUTH.REGISTER);
    }
  }, [router]);

  const handleResendActivation = async () => {
    if (!email) return;

    setIsResending(true);
    try {
      await apiClient.resendActivation(email);
      setEmailSent(true);
      // Vymaž email ze sessionStorage po úspěšném odeslání
      sessionStorage.removeItem('resendActivationEmail');
      toast.success(
        'Nový aktivační email byl odeslán. Zkontrolujte svou emailovou schránku.'
      );
    } catch (error: any) {
      const errorData = error.response?.data as ApiError;
      if (errorData?.error && errorData.message) {
        toast.error(errorData.message);
      } else {
        toast.error('Chyba při odesílání aktivačního emailu');
      }
    } finally {
      setIsResending(false);
    }
  };

  // Pokud email ještě není načten, zobrazuj loading
  if (!email) {
    return (
      <Layout>
        <div className="flex min-h-screen flex-col justify-center bg-gray-50 py-12 sm:px-6 lg:px-8">
          <div className="text-center sm:mx-auto sm:w-full sm:max-w-md">
            <div className="mx-auto h-8 w-8 animate-spin rounded-full border-b-2 border-primary-600"></div>
            <p className="mt-2 text-sm text-gray-600">Načítám...</p>
          </div>
        </div>
      </Layout>
    );
  }

  if (emailSent) {
    return (
      <Layout>
        <div className="flex min-h-screen flex-col justify-center bg-gray-50 py-12 sm:px-6 lg:px-8">
          <div className="sm:mx-auto sm:w-full sm:max-w-md">
            <div className="text-center">
              <svg
                className="mx-auto h-12 w-12 text-green-400"
                fill="none"
                stroke="currentColor"
                viewBox="0 0 48 48"
              >
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth={2}
                  d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"
                />
              </svg>
              <h2 className="mt-6 text-center text-3xl font-bold tracking-tight text-gray-900">
                Email byl odeslán
              </h2>
              <p className="mt-2 text-center text-sm text-gray-600">
                Nový aktivační email byl odeslán na vaši emailovou adresu.
                Zkontrolujte svou schránku a klikněte na ověřovací odkaz.
              </p>
            </div>

            <div className="mt-8 text-center">
              <Link
                href={ROUTES.AUTH.LOGIN}
                className="font-medium text-primary-600 hover:text-primary-500"
              >
                Přejít na přihlášení
              </Link>
            </div>
          </div>
        </div>
      </Layout>
    );
  }

  return (
    <Layout>
      <div className="flex min-h-screen flex-col justify-center bg-gray-50 py-12 sm:px-6 lg:px-8">
        <div className="sm:mx-auto sm:w-full sm:max-w-md">
          <div className="text-center">
            <svg
              className="mx-auto h-12 w-12 text-yellow-400"
              fill="none"
              stroke="currentColor"
              viewBox="0 0 48 48"
            >
              <path
                strokeLinecap="round"
                strokeLinejoin="round"
                strokeWidth={2}
                d="M12 9v3m0 0v3m0-3h3m-3 0H9m12 0a9 9 0 11-18 0 9 9 0 0118 0z"
              />
            </svg>
            <h2 className="mt-6 text-center text-3xl font-bold tracking-tight text-gray-900">
              Účet není aktivován
            </h2>
            <p className="mt-2 text-center text-sm text-gray-600">
              Uživatel s tímto emailem již existuje, ale není ověřený. Můžete si
              nechat odeslat nový aktivační email.
            </p>
          </div>
        </div>

        <div className="mt-8 sm:mx-auto sm:w-full sm:max-w-md">
          <div className="bg-white px-4 py-8 shadow sm:rounded-lg sm:px-10">
            <div className="space-y-6">
              <div className="text-center">
                <p className="mb-4 text-sm text-gray-600">
                  Email:{' '}
                  <span className="font-medium text-gray-900">{email}</span>
                </p>
              </div>

              <div>
                <button
                  onClick={handleResendActivation}
                  disabled={isResending}
                  className="btn btn-primary w-full disabled:cursor-not-allowed disabled:opacity-50"
                >
                  {isResending ? 'Odesílám...' : 'Odeslat nový aktivační email'}
                </button>
              </div>
            </div>

            <div className="mt-6">
              <div className="relative">
                <div className="absolute inset-0 flex items-center">
                  <div className="w-full border-t border-gray-300" />
                </div>
                <div className="relative flex justify-center text-sm">
                  <span className="bg-white px-2 text-gray-500">Nebo</span>
                </div>
              </div>

              <div className="mt-6 text-center">
                <Link
                  href={ROUTES.AUTH.REGISTER}
                  className="font-medium text-primary-600 hover:text-primary-500"
                >
                  Registrovat nový účet
                </Link>
              </div>
            </div>
          </div>
        </div>
      </div>
    </Layout>
  );
}
