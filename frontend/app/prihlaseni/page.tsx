'use client';

import { useEffect, useState } from 'react';
import { useRouter, useSearchParams } from 'next/navigation';
import Link from 'next/link';
import { useForm } from 'react-hook-form';
import toast from 'react-hot-toast';
import { LoginData } from '@/types';
import Layout from '@/components/layout/Layout';
import { ROUTES } from '@/lib/routes';
import { useAuth } from '@/hooks/useAuth';

export default function LoginPage() {
  const router = useRouter();
  const searchParams = useSearchParams();
  const { isAuthenticated, isLoadingUser, login, isLoggingIn } = useAuth();
  const [errorMessage, setErrorMessage] = useState<string | null>(null);

  // Načti chybovou hlášku z localStorage při načtení komponenty
  useEffect(() => {
    const savedError = localStorage.getItem('loginError');
    if (savedError) {
      setErrorMessage(savedError);
      localStorage.removeItem('loginError'); // Zruš po načtení
    }
  }, []);

  // Přesměruj přihlášené uživatele na nástěnku
  useEffect(() => {
    if (!isLoadingUser && isAuthenticated) {
      router.push(ROUTES.USER.DASHBOARD);
      return;
    }
  }, [isAuthenticated, isLoadingUser, router]);

  const {
    register,
    handleSubmit,
    formState: { errors },
  } = useForm<LoginData>();

  // Odstraněno - způsobovalo konflikty s vlastním error handlingem

  const onSubmit = async (data: LoginData) => {
    // Zruš předchozí chybovou hlášku
    setErrorMessage(null);
    localStorage.removeItem('loginError');

    try {
      await login(data);
      toast.success('Úspěšně přihlášen!');

      // Přesměruj zpět na původní stránku nebo na dashboard
      const redirectTo = searchParams.get('redirect') || ROUTES.USER.DASHBOARD;
      router.push(redirectTo);
    } catch (error: any) {
      let message = error.response?.data?.message || 'Chyba při přihlašování';

      // Speciální zpracování pro neověřený email
      if (error.response?.data?.code === 'EMAIL_NOT_VERIFIED') {
        message =
          error.response.data.message +
          ' Pokud jste email neobdrželi, zaregistrujte se znovu.';
      }

      // Použij setTimeout pro zabránění problikávání
      setTimeout(() => {
        localStorage.setItem('loginError', message);
        setErrorMessage(message);
      }, 100);
    }
  };

  return (
    <Layout>
      <div className="flex min-h-screen flex-col justify-center bg-gray-50 py-12 sm:px-6 lg:px-8">
        <div className="sm:mx-auto sm:w-full sm:max-w-md">
          <Link href={ROUTES.HOME} className="flex justify-center">
            <h1 className="text-3xl font-bold text-primary-600">Pujčovna</h1>
          </Link>
          <h2 className="mt-6 text-center text-3xl font-extrabold text-gray-900">
            Přihlášení do účtu
          </h2>
          <p className="mt-2 text-center text-sm text-gray-600">
            Nebo{' '}
            <Link
              href={ROUTES.AUTH.REGISTER}
              className="font-medium text-primary-600 hover:text-primary-500"
            >
              si vytvořte nový účet
            </Link>
          </p>
        </div>

        <div className="mt-8 sm:mx-auto sm:w-full sm:max-w-md">
          <div className="bg-white px-4 py-8 shadow sm:rounded-lg sm:px-10">
            {errorMessage && (
              <div className="mb-6 rounded-md bg-red-50 p-4">
                <div className="flex">
                  <div className="flex-shrink-0">
                    <svg
                      className="h-5 w-5 text-red-400"
                      viewBox="0 0 20 20"
                      fill="currentColor"
                    >
                      <path
                        fillRule="evenodd"
                        d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z"
                        clipRule="evenodd"
                      />
                    </svg>
                  </div>
                  <div className="ml-3">
                    <h3 className="text-sm font-medium text-red-800">
                      {errorMessage}
                    </h3>
                  </div>
                  <div className="ml-auto pl-3">
                    <div className="-mx-1.5 -my-1.5">
                      <button
                        type="button"
                        onClick={() => {
                          setErrorMessage(null);
                          localStorage.removeItem('loginError');
                        }}
                        className="inline-flex rounded-md bg-red-50 p-1.5 text-red-500 hover:bg-red-100 focus:outline-none focus:ring-2 focus:ring-red-600 focus:ring-offset-2 focus:ring-offset-red-50"
                      >
                        <span className="sr-only">Zavřít</span>
                        <svg
                          className="h-5 w-5"
                          viewBox="0 0 20 20"
                          fill="currentColor"
                        >
                          <path
                            fillRule="evenodd"
                            d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z"
                            clipRule="evenodd"
                          />
                        </svg>
                      </button>
                    </div>
                  </div>
                </div>
              </div>
            )}
            <form className="space-y-6" onSubmit={handleSubmit(onSubmit)}>
              <div>
                <label
                  htmlFor="email"
                  className="block text-sm font-medium text-gray-700"
                >
                  Email
                </label>
                <div className="mt-1">
                  <input
                    {...register('email', {
                      required: 'Email je povinný',
                      pattern: {
                        value: /^[A-Z0-9._%+-]+@[A-Z0-9.-]+\.[A-Z]{2,}$/i,
                        message: 'Neplatný email',
                      },
                      onChange: () => {
                        // Zruš chybovou hlášku při změně emailu
                        if (errorMessage) {
                          setErrorMessage(null);
                          localStorage.removeItem('loginError');
                        }
                      },
                    })}
                    type="email"
                    className="input"
                    placeholder="<EMAIL>"
                  />
                  {errors.email && (
                    <p className="mt-1 text-sm text-red-600">
                      {errors.email.message}
                    </p>
                  )}
                </div>
              </div>

              <div>
                <label
                  htmlFor="password"
                  className="block text-sm font-medium text-gray-700"
                >
                  Heslo
                </label>
                <div className="mt-1">
                  <input
                    {...register('password', {
                      onChange: () => {
                        // Zruš chybovou hlášku při změně hesla
                        if (errorMessage) {
                          setErrorMessage(null);
                          localStorage.removeItem('loginError');
                        }
                      },
                    })}
                    type="password"
                    className="input"
                    placeholder="••••••••"
                  />
                  {errors.password && (
                    <p className="mt-1 text-sm text-red-600">
                      {errors.password.message}
                    </p>
                  )}
                </div>
              </div>

              <div className="flex items-center justify-between">
                <div className="flex items-center">
                  <input
                    {...register('rememberMe')}
                    id="remember-me"
                    type="checkbox"
                    className="h-4 w-4 rounded border-gray-300 text-primary-600 focus:ring-primary-500"
                  />
                  <label
                    htmlFor="remember-me"
                    className="ml-2 block text-sm text-gray-900"
                  >
                    Zapamatovat si mě
                  </label>
                </div>

                <div className="text-sm">
                  <Link
                    href={ROUTES.AUTH.FORGOT_PASSWORD}
                    className="font-medium text-primary-600 hover:text-primary-500"
                  >
                    Zapomněli jste heslo?
                  </Link>
                </div>
              </div>

              <div>
                <button
                  type="submit"
                  disabled={isLoggingIn}
                  className="btn btn-primary w-full disabled:cursor-not-allowed disabled:opacity-50"
                >
                  {isLoggingIn ? 'Přihlašuji...' : 'Přihlásit se'}
                </button>
              </div>
            </form>

            <div className="mt-6">
              <div className="relative">
                <div className="absolute inset-0 flex items-center">
                  <div className="w-full border-t border-gray-300" />
                </div>
                <div className="relative flex justify-center text-sm">
                  <span className="bg-white px-2 text-gray-500">
                    Nebo pokračujte s
                  </span>
                </div>
              </div>

              <div className="mt-6 grid grid-cols-2 gap-3">
                <button className="inline-flex w-full justify-center rounded-md border border-gray-300 bg-white px-4 py-2 text-sm font-medium text-gray-500 shadow-sm hover:bg-gray-50">
                  Google
                </button>
                <button className="inline-flex w-full justify-center rounded-md border border-gray-300 bg-white px-4 py-2 text-sm font-medium text-gray-500 shadow-sm hover:bg-gray-50">
                  Facebook
                </button>
              </div>
            </div>
          </div>
        </div>
      </div>
    </Layout>
  );
}
