'use client';

import { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import { useForm } from 'react-hook-form';
import {
  ArrowLeft,
  Package,
  DollarSign,
  FileText,
  Tag,
  MapPin,
} from 'lucide-react';
import toast from 'react-hot-toast';
import { useAuth } from '@/hooks/useAuth';
import { useCreateItem } from '@/hooks/useItems';
import { useCategories } from '@/hooks/useCategories';
import { useUpdateProfile } from '@/hooks/useProfile';
import Layout from '@/components/layout/Layout';
import { ROUTES } from '@/lib/routes';
import Button from '@/components/ui/Button';
import { ItemImageManager } from '@/components/images';
import AddressAutocomplete from '@/components/ui/AddressAutocomplete';

interface CreateItemFormData {
  title: string;
  description: string;
  categoryId: number;
  pricePerDay: number;
  deposit?: number;
  condition: 'excellent' | 'good' | 'fair' | 'poor';
}

export default function CreateItemPage() {
  const router = useRouter();
  const { user, isAuthenticated, isLoadingUser } = useAuth();
  const [isLoading, setIsLoading] = useState(false);
  const [createdItemId, setCreatedItemId] = useState<number | null>(null);
  const [showAddressForm, setShowAddressForm] = useState(false);
  const [addressData, setAddressData] = useState({
    street: '',
    city: '',
    zipCode: '',
  });
  const [addressInput, setAddressInput] = useState('');

  const {
    register,
    handleSubmit,
    formState: { errors },
  } = useForm<CreateItemFormData>();

  const createItemMutation = useCreateItem();
  const updateProfileMutation = useUpdateProfile();
  const { data: categories = [], isLoading: isLoadingCategories } =
    useCategories();

  // Přesměruj nepřihlášené uživatele
  useEffect(() => {
    if (!isLoadingUser && !isAuthenticated) {
      router.push(
        `${ROUTES.AUTH.LOGIN}?redirect=${encodeURIComponent(ROUTES.ITEMS.CREATE)}`
      );
    }
  }, [isAuthenticated, isLoadingUser, router]);

  // Kontrola adresy uživatele
  useEffect(() => {
    if (user && !user.address) {
      setShowAddressForm(true);
    }
  }, [user]);

  // Zobraz loading během načítání
  if (isLoadingUser) {
    return (
      <Layout>
        <div className="flex min-h-screen items-center justify-center bg-gray-50">
          <div className="text-center">
            <div className="mx-auto h-32 w-32 animate-spin rounded-full border-b-2 border-primary-600"></div>
            <p className="mt-4 text-gray-600">Načítám...</p>
          </div>
        </div>
      </Layout>
    );
  }

  // Pokud není přihlášený, nezobrazuj nic
  if (!isAuthenticated) {
    return null;
  }

  const handleAddressSelect = (address: any) => {
    setAddressData({
      street: address.street,
      city: address.city,
      zipCode: address.zipCode,
    });
    setAddressInput(address.street + ', ' + address.city);
  };

  const handleAddressChange = (field: string, value: string) => {
    setAddressData((prev) => ({
      ...prev,
      [field]: value,
    }));
  };

  const saveAddress = async () => {
    if (
      !user ||
      !addressData.street ||
      !addressData.city ||
      !addressData.zipCode
    ) {
      toast.error('Vyplňte všechna pole adresy');
      return;
    }

    try {
      await updateProfileMutation.mutateAsync({
        firstName: user.firstName,
        lastName: user.lastName,
        email: user.email,
        phone: user.phone || '',
        address: addressData,
      });
      setShowAddressForm(false);
      toast.success('Adresa byla úspěšně uložena!');
    } catch (error: any) {
      console.error('Chyba při ukládání adresy:', error);
      toast.error('Chyba při ukládání adresy');
    }
  };

  const onSubmit = async (data: CreateItemFormData) => {
    setIsLoading(true);
    try {
      const itemData = {
        title: data.title,
        description: data.description,
        categoryId: data.categoryId,
        pricePerDay: data.pricePerDay,
        deposit: data.deposit || 0,
        condition: data.condition,
      };

      const createdItem = await createItemMutation.mutateAsync(itemData);
      setCreatedItemId(createdItem.id);
      toast.success('Předmět byl úspěšně vytvořen! Nyní můžete přidat fotografie.');
    } catch (error: any) {
      console.error('Chyba při vytváření předmětu:', error);
      toast.error(
        error.response?.data?.message || 'Chyba při vytváření předmětu'
      );
    } finally {
      setIsLoading(false);
    }
  };

  if (isLoadingUser || isLoadingCategories) {
    return (
      <Layout>
        <div className="flex min-h-screen items-center justify-center">
          <div className="h-32 w-32 animate-spin rounded-full border-b-2 border-primary-600"></div>
        </div>
      </Layout>
    );
  }

  return (
    <Layout>
      <div className="min-h-screen bg-gray-50">
        <div className="mx-auto max-w-2xl px-4 py-8 sm:px-6 lg:px-8">
          {/* Header */}
          <div className="mb-8">
            <button
              onClick={() => router.back()}
              className="mb-4 flex items-center text-gray-600 hover:text-gray-900"
            >
              <ArrowLeft className="mr-2 h-5 w-5" />
              Zpět
            </button>
            <h1 className="text-3xl font-bold text-gray-900">
              Přidat nový předmět
            </h1>
            <p className="mt-2 text-gray-600">
              Vyplňte informace o předmětu, který chcete půjčovat ostatním.
            </p>
          </div>

          {/* Address Form */}
          {showAddressForm && (
            <div className="mb-6 rounded-lg border border-blue-200 bg-blue-50 p-6 shadow-sm">
              <div className="mb-4 flex items-center">
                <MapPin className="mr-2 h-5 w-5 text-blue-600" />
                <h2 className="text-lg font-semibold text-blue-900">
                  Nastavte svou adresu
                </h2>
              </div>
              <p className="mb-4 text-blue-700">
                Pro vytvoření předmětu je potřeba mít nastavenou adresu. Tato
                adresa bude použita pro všechny vaše předměty.
              </p>

              <div className="space-y-4">
                <div>
                  <label className="mb-2 block text-sm font-medium text-gray-700">
                    Vyhledat adresu
                  </label>
                  <AddressAutocomplete
                    onAddressSelect={handleAddressSelect}
                    value={addressInput}
                    onChange={setAddressInput}
                    placeholder="Začněte psát adresu..."
                  />
                </div>

                <div>
                  <label className="mb-1 block text-sm font-medium text-gray-700">
                    Ulice a číslo popisné
                  </label>
                  <input
                    type="text"
                    value={addressData.street}
                    onChange={(e) =>
                      handleAddressChange('street', e.target.value)
                    }
                    className="w-full rounded-md border border-gray-300 px-3 py-2 focus:border-transparent focus:ring-2 focus:ring-primary-500"
                    placeholder="Např. Václavské náměstí 1"
                  />
                </div>

                <div className="grid grid-cols-2 gap-4">
                  <div>
                    <label className="mb-1 block text-sm font-medium text-gray-700">
                      Město
                    </label>
                    <input
                      type="text"
                      value={addressData.city}
                      onChange={(e) =>
                        handleAddressChange('city', e.target.value)
                      }
                      className="w-full rounded-md border border-gray-300 px-3 py-2 focus:border-transparent focus:ring-2 focus:ring-primary-500"
                      placeholder="Praha"
                    />
                  </div>
                  <div>
                    <label className="mb-1 block text-sm font-medium text-gray-700">
                      PSČ
                    </label>
                    <input
                      type="text"
                      value={addressData.zipCode}
                      onChange={(e) =>
                        handleAddressChange('zipCode', e.target.value)
                      }
                      className="w-full rounded-md border border-gray-300 px-3 py-2 focus:border-transparent focus:ring-2 focus:ring-primary-500"
                      placeholder="11000"
                    />
                  </div>
                </div>

                <div className="flex justify-end space-x-3">
                  <Button
                    type="button"
                    variant="outline"
                    onClick={() => setShowAddressForm(false)}
                  >
                    Zrušit
                  </Button>
                  <Button
                    type="button"
                    onClick={saveAddress}
                    disabled={updateProfileMutation.isLoading}
                  >
                    {updateProfileMutation.isLoading
                      ? 'Ukládám...'
                      : 'Uložit adresu'}
                  </Button>
                </div>
              </div>
            </div>
          )}

          {/* Form */}
          <div
            className={`rounded-lg bg-white p-6 shadow-sm ${showAddressForm ? 'pointer-events-none opacity-50' : ''}`}
          >
            {showAddressForm && (
              <div className="mb-4 rounded-md border border-yellow-200 bg-yellow-50 p-4">
                <p className="text-sm text-yellow-800">
                  Nejprve nastavte svou adresu výše, poté budete moci vytvořit
                  předmět.
                </p>
              </div>
            )}
            <form
              method="POST"
              onSubmit={handleSubmit(onSubmit)}
              className="space-y-6"
            >
              {/* Název */}
              <div>
                <label className="mb-2 block text-sm font-medium text-gray-700">
                  <Package className="mr-1 inline h-4 w-4" />
                  Název předmětu *
                </label>
                <input
                  {...register('title', {
                    required: 'Název je povinný',
                    minLength: {
                      value: 3,
                      message: 'Název musí mít alespoň 3 znaky',
                    },
                  })}
                  type="text"
                  className="input"
                  placeholder="Např. Vrtačka Bosch Professional"
                />
                {errors.title && (
                  <p className="mt-1 text-sm text-red-600">
                    {errors.title.message}
                  </p>
                )}
              </div>

              {/* Popis */}
              <div>
                <label className="mb-2 block text-sm font-medium text-gray-700">
                  <FileText className="mr-1 inline h-4 w-4" />
                  Popis *
                </label>
                <textarea
                  {...register('description', {
                    required: 'Popis je povinný',
                    minLength: {
                      value: 10,
                      message: 'Popis musí mít alespoň 10 znaků',
                    },
                  })}
                  rows={4}
                  className="input"
                  placeholder="Popište váš předmět, jeho stav, co je v balení, případné omezení použití..."
                />
                {errors.description && (
                  <p className="mt-1 text-sm text-red-600">
                    {errors.description.message}
                  </p>
                )}
              </div>

              {/* Kategorie */}
              <div>
                <label className="mb-2 block text-sm font-medium text-gray-700">
                  <Tag className="mr-1 inline h-4 w-4" />
                  Kategorie *
                </label>
                <select
                  {...register('categoryId', {
                    required: 'Kategorie je povinná',
                    valueAsNumber: true,
                  })}
                  className="input"
                >
                  <option value="">Vyberte kategorii</option>
                  {categories.map((category) => (
                    <option key={category.id} value={category.id}>
                      {category.name}
                    </option>
                  ))}
                </select>
                {errors.categoryId && (
                  <p className="mt-1 text-sm text-red-600">
                    {errors.categoryId.message}
                  </p>
                )}
              </div>

              {/* Cena a záloha */}
              <div className="grid grid-cols-1 gap-4 md:grid-cols-2">
                <div>
                  <label className="mb-2 block text-sm font-medium text-gray-700">
                    <DollarSign className="mr-1 inline h-4 w-4" />
                    Cena za den (Kč) *
                  </label>
                  <input
                    {...register('pricePerDay', {
                      required: 'Cena je povinná',
                      valueAsNumber: true,
                      min: {
                        value: 1,
                        message: 'Cena musí být alespoň 1 Kč',
                      },
                    })}
                    type="number"
                    min="1"
                    step="1"
                    className="input"
                    placeholder="100"
                  />
                  {errors.pricePerDay && (
                    <p className="mt-1 text-sm text-red-600">
                      {errors.pricePerDay.message}
                    </p>
                  )}
                </div>

                <div>
                  <label className="mb-2 block text-sm font-medium text-gray-700">
                    Záloha (Kč)
                  </label>
                  <input
                    {...register('deposit', {
                      valueAsNumber: true,
                      min: {
                        value: 0,
                        message: 'Záloha nemůže být záporná',
                      },
                    })}
                    type="number"
                    min="0"
                    step="1"
                    className="input"
                    placeholder="500"
                  />
                  {errors.deposit && (
                    <p className="mt-1 text-sm text-red-600">
                      {errors.deposit.message}
                    </p>
                  )}
                </div>
              </div>

              {/* Stav */}
              <div>
                <label className="mb-2 block text-sm font-medium text-gray-700">
                  Stav předmětu *
                </label>
                <select
                  {...register('condition', {
                    required: 'Stav předmětu je povinný',
                  })}
                  className="input"
                >
                  <option value="">Vyberte stav</option>
                  <option value="excellent">Výborný</option>
                  <option value="good">Dobrý</option>
                  <option value="fair">Průměrný</option>
                  <option value="poor">Špatný</option>
                </select>
                {errors.condition && (
                  <p className="mt-1 text-sm text-red-600">
                    {errors.condition.message}
                  </p>
                )}
              </div>

              {/* Fotografie - zobrazí se až po vytvoření produktu */}
              {createdItemId && (
                <div>
                  <label className="mb-2 block text-sm font-medium text-gray-700">
                    Fotografie produktu
                  </label>
                  <ItemImageManager
                    itemId={createdItemId}
                    canEdit={true}
                    maxImages={6}
                  />
                </div>
              )}

              {/* Tlačítka */}
              <div className="flex justify-end space-x-4 pt-6">
                {!createdItemId ? (
                  <>
                    <Button
                      type="button"
                      variant="outline"
                      onClick={() => router.back()}
                      disabled={isLoading}
                    >
                      Zrušit
                    </Button>
                    <Button
                      type="submit"
                      disabled={isLoading}
                      className="min-w-[120px]"
                    >
                      {isLoading ? 'Vytváří se...' : 'Vytvořit předmět'}
                    </Button>
                  </>
                ) : (
                  <Button
                    type="button"
                    onClick={() => router.push(ROUTES.USER.DASHBOARD)}
                    className="min-w-[120px]"
                  >
                    Dokončit
                  </Button>
                )}
              </div>
            </form>
          </div>
        </div>
      </div>
    </Layout>
  );
}
