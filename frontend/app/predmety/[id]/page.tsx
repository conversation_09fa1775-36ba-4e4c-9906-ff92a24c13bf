'use client';

import { useState, useEffect } from 'react';
import { useRouter, useParams } from 'next/navigation';
import { Calendar, MapPin, User, Edit, Trash2 } from 'lucide-react';
import { useAuth } from '@/hooks/useAuth';
import { useItem, useDeleteItem } from '@/hooks/useItems';
import { ROUTES } from '@/lib/routes';
import Layout from '@/components/layout/Layout';
import BookingForm from '@/components/forms/BookingForm';
import { getItemPrice } from '@/lib/price-utils';

export default function PredmetDetailPage() {
  const router = useRouter();
  const params = useParams();
  const { user, isAuthenticated } = useAuth();
  const [showBookingForm, setShowBookingForm] = useState(false);

  const itemId = Number(params.id);

  // Kontrola speciálních rout
  useEffect(() => {
    if (params.id === 'vytvorit' || params.id === 'novy') {
      router.replace(ROUTES.ITEMS.CREATE);
      return;
    }
  }, [params.id, router]);

  // Použij React Query hook místo přímého volání API
  const { data: item, isLoading: loading, error } = useItem(itemId);
  const deleteItemMutation = useDeleteItem();

  const handleEdit = () => {
    router.push(`/predmety/${itemId}/upravit`);
  };

  const handleDelete = async () => {
    if (!confirm('Opravdu chcete smazat tento předmět?')) {
      return;
    }

    try {
      await deleteItemMutation.mutateAsync(itemId);
      router.push(ROUTES.ITEMS.LIST);
    } catch (error) {
      console.error('Chyba při mazání předmětu:', error);
      alert('Chyba při mazání předmětu');
    }
  };

  const handleBookingClick = () => {
    if (!isAuthenticated) {
      // Přesměruj na přihlášení s návratem zpět
      const currentPath = `/predmety/${itemId}`;
      router.push(
        `${ROUTES.AUTH.LOGIN}?redirect=${encodeURIComponent(currentPath)}`
      );
      return;
    }

    setShowBookingForm(true);
  };

  const handleBookingSuccess = () => {
    setShowBookingForm(false);
    // Můžeme přesměrovat na stránku rezervací
    router.push(ROUTES.USER.BOOKINGS);
  };

  if (loading) {
    return (
      <Layout>
        <div className="flex min-h-screen items-center justify-center bg-gray-50">
          <div className="text-center">
            <div className="mx-auto h-32 w-32 animate-spin rounded-full border-b-2 border-primary-600"></div>
            <p className="mt-4 text-gray-600">Načítám předmět...</p>
          </div>
        </div>
      </Layout>
    );
  }

  if (error || (!loading && !item)) {
    return (
      <Layout>
        <div className="flex min-h-screen items-center justify-center bg-gray-50">
          <div className="text-center">
            <h1 className="mb-4 text-2xl font-bold text-gray-900">
              Předmět nebyl nalezen
            </h1>
            <button
              onClick={() => router.push(ROUTES.ITEMS.LIST)}
              className="rounded-md bg-primary-600 px-4 py-2 text-white hover:bg-primary-700"
            >
              Zpět na seznam
            </button>
          </div>
        </div>
      </Layout>
    );
  }

  const isOwner = isAuthenticated && user && item.owner?.id === user.id;

  return (
    <Layout>
      <div className="min-h-screen bg-gray-50 py-8">
        <div className="mx-auto max-w-4xl px-4 sm:px-6 lg:px-8">
          <div className="overflow-hidden rounded-lg bg-white shadow-sm">
            {/* Header */}
            <div className="border-b p-6">
              <div className="flex items-start justify-between">
                <div>
                  <h1 className="mb-2 text-3xl font-bold text-gray-900">
                    {item.title}
                  </h1>
                  <div className="flex items-center space-x-4 text-sm text-gray-600">
                    <span className="flex items-center">
                      <User className="mr-1 h-4 w-4" />
                      {item.owner?.firstName} {item.owner?.lastName}
                    </span>
                    <span className="flex items-center">
                      <MapPin className="mr-1 h-4 w-4" />
                      {item.owner?.address?.city || 'Neuvedeno'}
                    </span>
                    <span className="flex items-center">
                      <Calendar className="mr-1 h-4 w-4" />
                      {new Date(item.createdAt).toLocaleDateString('cs-CZ')}
                    </span>
                  </div>
                </div>

                {isOwner && (
                  <div className="flex space-x-2">
                    <button
                      onClick={handleEdit}
                      className="flex items-center rounded-md bg-blue-600 px-3 py-2 text-sm text-white hover:bg-blue-700"
                    >
                      <Edit className="mr-1 h-4 w-4" />
                      Upravit
                    </button>
                    <button
                      onClick={handleDelete}
                      className="flex items-center rounded-md bg-red-600 px-3 py-2 text-sm text-white hover:bg-red-700"
                    >
                      <Trash2 className="mr-1 h-4 w-4" />
                      Smazat
                    </button>
                  </div>
                )}
              </div>
            </div>

            {/* Content */}
            <div className="p-6">
              <div className="grid grid-cols-1 gap-8 lg:grid-cols-3">
                {/* Main content */}
                <div className="lg:col-span-2">
                  <div className="mb-6">
                    <h2 className="mb-3 text-xl font-semibold text-gray-900">
                      Popis
                    </h2>
                    <p className="leading-relaxed text-gray-700">
                      {item.description}
                    </p>
                  </div>

                  {item.conditions && (
                    <div className="mb-6">
                      <h2 className="mb-3 text-xl font-semibold text-gray-900">
                        Stav
                      </h2>
                      <p className="text-gray-700">{item.conditions}</p>
                    </div>
                  )}
                </div>

                {/* Sidebar */}
                <div className="lg:col-span-1">
                  <div className="rounded-lg bg-gray-50 p-6">
                    <div className="mb-6">
                      <h3 className="mb-3 text-lg font-semibold text-gray-900">
                        Cena
                      </h3>
                      <div className="flex items-center">
                        <span className="text-2xl font-bold text-primary-600">
                          {getItemPrice(item) || 'N/A'} Kč
                        </span>
                        <span className="ml-1 text-gray-600">/den</span>
                      </div>
                      {item.deposit && (
                        <p className="mt-2 text-sm text-gray-600">
                          Záloha: {item.deposit} Kč
                        </p>
                      )}
                    </div>

                    <div className="mb-6">
                      <h3 className="mb-3 text-lg font-semibold text-gray-900">
                        Kategorie
                      </h3>
                      <span className="inline-block rounded-full bg-primary-100 px-3 py-1 text-sm text-primary-800">
                        {item.category?.name}
                      </span>
                    </div>

                    <div className="mb-6">
                      <h3 className="mb-3 text-lg font-semibold text-gray-900">
                        Dostupnost
                      </h3>
                      <span
                        className={`inline-block rounded-full px-3 py-1 text-sm ${
                          item.isAvailable
                            ? 'bg-green-100 text-green-800'
                            : 'bg-red-100 text-red-800'
                        }`}
                      >
                        {item.isAvailable ? 'Dostupný' : 'Nedostupný'}
                      </span>
                    </div>

                    {!isOwner && item.isAvailable && (
                      <button
                        onClick={handleBookingClick}
                        className="w-full rounded-md bg-primary-600 px-4 py-3 font-medium text-white hover:bg-primary-700"
                      >
                        Půjčit si
                      </button>
                    )}
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      {showBookingForm && item && (
        <BookingForm
          item={item}
          onSuccess={handleBookingSuccess}
          onCancel={() => setShowBookingForm(false)}
        />
      )}
    </Layout>
  );
}
