'use client';

import { useState } from 'react';
import Layout from '@/components/layout/Layout';
import Button from '@/components/ui/Button';
import {
  Mail,
  Phone,
  MapPin,
  Clock,
  MessageCircle,
  Send,
  CheckCircle,
} from 'lucide-react';

export default function ContactPage() {
  const [formData, setFormData] = useState({
    name: '',
    email: '',
    subject: '',
    message: '',
    type: 'general',
  });
  const [isLoading, setIsLoading] = useState(false);
  const [isSubmitted, setIsSubmitted] = useState(false);
  const [error, setError] = useState('');

  const handleInputChange = (
    e: React.ChangeEvent<
      HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement
    >
  ) => {
    const { name, value } = e.target;
    setFormData((prev) => ({
      ...prev,
      [name]: value,
    }));
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setIsLoading(true);
    setError('');

    try {
      // TODO: Implementovat API volání pro odeslání kontaktního formuláře
      const response = await fetch('/api/v1/contact', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(formData),
      });

      if (response.ok) {
        setIsSubmitted(true);
        setFormData({
          name: '',
          email: '',
          subject: '',
          message: '',
          type: 'general',
        });
      } else {
        const data = await response.json();
        setError(data.message || 'Došlo k chybě při odesílání zprávy.');
      }
    } catch (err) {
      setError('Došlo k chybě při odesílání zprávy. Zkuste to prosím znovu.');
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <Layout>
      <div className="bg-gray-50">
        {/* Hero Section */}
        <section className="bg-primary-600 py-16 text-white">
          <div className="mx-auto max-w-7xl px-4 sm:px-6 lg:px-8">
            <div className="text-center">
              <h1 className="mb-6 text-4xl font-bold md:text-5xl">
                Kontaktujte nás
              </h1>
              <p className="mx-auto max-w-3xl text-xl text-primary-100">
                Máte otázky, návrhy nebo potřebujete pomoc? Rádi si s vámi
                promluvíme.
              </p>
            </div>
          </div>
        </section>

        {/* Contact Info */}
        <section className="py-16">
          <div className="mx-auto max-w-7xl px-4 sm:px-6 lg:px-8">
            <div className="grid grid-cols-1 gap-8 lg:grid-cols-3">
              {/* Contact Methods */}
              <div className="lg:col-span-1">
                <h2 className="mb-8 text-2xl font-bold text-gray-900">
                  Kontaktní údaje
                </h2>

                <div className="space-y-6">
                  <div className="flex items-start space-x-4">
                    <div className="flex h-12 w-12 items-center justify-center rounded-full bg-primary-100">
                      <Mail className="h-6 w-6 text-primary-600" />
                    </div>
                    <div>
                      <h3 className="font-semibold text-gray-900">Email</h3>
                      <p className="text-gray-600"><EMAIL></p>
                      <p className="text-sm text-gray-500">
                        Odpovídáme do 24 hodin
                      </p>
                    </div>
                  </div>

                  <div className="flex items-start space-x-4">
                    <div className="flex h-12 w-12 items-center justify-center rounded-full bg-green-100">
                      <Phone className="h-6 w-6 text-green-600" />
                    </div>
                    <div>
                      <h3 className="font-semibold text-gray-900">Telefon</h3>
                      <p className="text-gray-600">+420 123 456 789</p>
                      <p className="text-sm text-gray-500">Po-Pá 9:00-17:00</p>
                    </div>
                  </div>

                  <div className="flex items-start space-x-4">
                    <div className="flex h-12 w-12 items-center justify-center rounded-full bg-blue-100">
                      <MapPin className="h-6 w-6 text-blue-600" />
                    </div>
                    <div>
                      <h3 className="font-semibold text-gray-900">Adresa</h3>
                      <p className="text-gray-600">
                        Wenceslas Square 1<br />
                        110 00 Praha 1<br />
                        Česká republika
                      </p>
                    </div>
                  </div>

                  <div className="flex items-start space-x-4">
                    <div className="flex h-12 w-12 items-center justify-center rounded-full bg-yellow-100">
                      <Clock className="h-6 w-6 text-yellow-600" />
                    </div>
                    <div>
                      <h3 className="font-semibold text-gray-900">
                        Pracovní doba
                      </h3>
                      <p className="text-gray-600">
                        Pondělí - Pátek: 9:00 - 17:00
                        <br />
                        Sobota - Neděle: Zavřeno
                      </p>
                    </div>
                  </div>
                </div>

                {/* Emergency Contact */}
                <div className="mt-8 rounded-lg bg-red-50 p-6">
                  <h3 className="mb-2 font-semibold text-red-900">
                    Naléhavé problémy
                  </h3>
                  <p className="mb-4 text-sm text-red-700">
                    Pro naléhavé problémy s bezpečností nebo podvody nás
                    kontaktujte okamžitě:
                  </p>
                  <a
                    href="tel:+420123456789"
                    className="inline-flex items-center text-red-600 hover:text-red-500"
                  >
                    <Phone className="mr-2 h-4 w-4" />
                    +420 123 456 789
                  </a>
                </div>
              </div>

              {/* Contact Form */}
              <div className="lg:col-span-2">
                <div className="rounded-lg bg-white p-8 shadow-sm">
                  <h2 className="mb-6 text-2xl font-bold text-gray-900">
                    Napište nám
                  </h2>

                  {isSubmitted && (
                    <div className="mb-6 rounded-md bg-green-50 p-4">
                      <div className="flex items-center">
                        <CheckCircle className="mr-3 h-5 w-5 text-green-600" />
                        <div className="text-sm text-green-700">
                          Vaša zpráva byla úspěšně odeslána. Odpovíme vám co
                          nejdříve.
                        </div>
                      </div>
                    </div>
                  )}

                  {error && (
                    <div className="mb-6 rounded-md bg-red-50 p-4">
                      <div className="text-sm text-red-700">{error}</div>
                    </div>
                  )}

                  <form
                    method="POST"
                    onSubmit={handleSubmit}
                    className="space-y-6"
                  >
                    <div className="grid grid-cols-1 gap-6 sm:grid-cols-2">
                      <div>
                        <label
                          htmlFor="name"
                          className="block text-sm font-medium text-gray-700"
                        >
                          Jméno a příjmení *
                        </label>
                        <input
                          type="text"
                          id="name"
                          name="name"
                          required
                          value={formData.name}
                          onChange={handleInputChange}
                          className="mt-1 block w-full rounded-md border border-gray-300 px-3 py-2 focus:border-primary-500 focus:outline-none focus:ring-1 focus:ring-primary-500"
                        />
                      </div>

                      <div>
                        <label
                          htmlFor="email"
                          className="block text-sm font-medium text-gray-700"
                        >
                          Email *
                        </label>
                        <input
                          type="email"
                          id="email"
                          name="email"
                          required
                          value={formData.email}
                          onChange={handleInputChange}
                          className="mt-1 block w-full rounded-md border border-gray-300 px-3 py-2 focus:border-primary-500 focus:outline-none focus:ring-1 focus:ring-primary-500"
                        />
                      </div>
                    </div>

                    <div>
                      <label
                        htmlFor="type"
                        className="block text-sm font-medium text-gray-700"
                      >
                        Typ dotazu
                      </label>
                      <select
                        id="type"
                        name="type"
                        value={formData.type}
                        onChange={handleInputChange}
                        className="mt-1 block w-full rounded-md border border-gray-300 px-3 py-2 focus:border-primary-500 focus:outline-none focus:ring-1 focus:ring-primary-500"
                      >
                        <option value="general">Obecný dotaz</option>
                        <option value="technical">Technický problém</option>
                        <option value="billing">Platby a fakturace</option>
                        <option value="safety">Bezpečnost</option>
                        <option value="partnership">Partnerství</option>
                        <option value="press">Média a tiskové zprávy</option>
                      </select>
                    </div>

                    <div>
                      <label
                        htmlFor="subject"
                        className="block text-sm font-medium text-gray-700"
                      >
                        Předmět *
                      </label>
                      <input
                        type="text"
                        id="subject"
                        name="subject"
                        required
                        value={formData.subject}
                        onChange={handleInputChange}
                        className="mt-1 block w-full rounded-md border border-gray-300 px-3 py-2 focus:border-primary-500 focus:outline-none focus:ring-1 focus:ring-primary-500"
                      />
                    </div>

                    <div>
                      <label
                        htmlFor="message"
                        className="block text-sm font-medium text-gray-700"
                      >
                        Zpráva *
                      </label>
                      <textarea
                        id="message"
                        name="message"
                        rows={6}
                        required
                        value={formData.message}
                        onChange={handleInputChange}
                        className="mt-1 block w-full rounded-md border border-gray-300 px-3 py-2 focus:border-primary-500 focus:outline-none focus:ring-1 focus:ring-primary-500"
                        placeholder="Popište váš dotaz nebo problém co nejpodrobněji..."
                      />
                    </div>

                    <div>
                      <Button
                        type="submit"
                        disabled={isLoading}
                        className="inline-flex items-center"
                      >
                        {isLoading ? (
                          'Odesílám...'
                        ) : (
                          <>
                            <Send className="mr-2 h-4 w-4" />
                            Odeslat zprávu
                          </>
                        )}
                      </Button>
                    </div>
                  </form>
                </div>
              </div>
            </div>
          </div>
        </section>

        {/* FAQ Link */}
        <section className="bg-white py-16">
          <div className="mx-auto max-w-7xl px-4 sm:px-6 lg:px-8">
            <div className="text-center">
              <h2 className="mb-4 text-2xl font-bold text-gray-900">
                Možná najdete odpověď rychleji
              </h2>
              <p className="mb-8 text-gray-600">
                Podívejte se na naše často kladené otázky nebo průvodce
                používáním
              </p>
              <div className="flex flex-col justify-center gap-4 sm:flex-row">
                <a
                  href="/napoveda"
                  className="btn btn-primary inline-flex items-center"
                >
                  <MessageCircle className="mr-2 h-4 w-4" />
                  Nápověda a FAQ
                </a>
                <a
                  href="/jak-to-funguje"
                  className="btn btn-outline inline-flex items-center"
                >
                  Jak to funguje
                </a>
              </div>
            </div>
          </div>
        </section>
      </div>
    </Layout>
  );
}
