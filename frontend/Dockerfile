FROM node:18-alpine

WORKDIR /app

# Kopírování package.json
COPY package.json ./

# Vytvoření prázdného package-lock.json pokud neexistuje
RUN touch package-lock.json

# Instalace závislostí
RUN npm install

# Kopírování zdrojového kódu
COPY . .

# Expose port
EXPOSE 3000

# Spuštění podle NODE_ENV
CMD if [ "$NODE_ENV" = "production" ]; then \
      npm run build && npm run start; \
    else \
      npm run dev; \
    fi
