import axios, { AxiosInstance } from 'axios';
import Cookies from 'js-cookie';
import {
  AuthResponse,
  LoginData,
  RegisterData,
  UpdateProfileData,
  User,
  Item,
  Category,
  Booking,
  Review,
  ApiResponse,
} from '@/types';

// Globální loading state pro progress bar
let loadingCount = 0;
const loadingCallbacks: Array<(isLoading: boolean) => void> = [];

export const subscribeToLoading = (callback: (isLoading: boolean) => void) => {
  loadingCallbacks.push(callback);
  return () => {
    const index = loadingCallbacks.indexOf(callback);
    if (index > -1) {
      loadingCallbacks.splice(index, 1);
    }
  };
};

const setLoading = (isLoading: boolean) => {
  if (isLoading) {
    loadingCount++;
  } else {
    loadingCount = Math.max(0, loadingCount - 1);
  }

  const shouldShowLoading = loadingCount > 0;
  loadingCallbacks.forEach((callback) => callback(shouldShowLoading));
};

class ApiClient {
  private client: AxiosInstance;

  constructor() {
    this.client = axios.create({
      baseURL:
        process.env.NEXT_PUBLIC_API_URL || 'http://localhost:18080/api/v1',
      headers: {
        'Content-Type': 'application/json',
      },
    });

    // Request interceptor pro loading a auth
    this.client.interceptors.request.use((config) => {
      setLoading(true);
      const token = Cookies.get('auth_token');
      if (token) {
        config.headers.Authorization = `Bearer ${token}`;
      }
      return config;
    });

    // Response interceptor pro ukončení loading
    this.client.interceptors.response.use(
      (response) => {
        setLoading(false);
        return response;
      },
      (error) => {
        setLoading(false);
        return Promise.reject(error);
      }
    );

    this.client.interceptors.response.use(
      (response) => response,
      async (error) => {
        if (error.response?.status === 401) {
          // Zabezpečené odstranění cookies při 401 chybě
          const cookieOptions = {
            secure: process.env.NODE_ENV === 'production',
            sameSite: 'strict' as const,
          };
          Cookies.remove('auth_token', cookieOptions);
          Cookies.remove('refresh_token', cookieOptions);
          window.location.href = '/prihlaseni';
        }
        return Promise.reject(error);
      }
    );
  }

  // Auth endpoints
  async register(data: RegisterData): Promise<{ user: User; message: string }> {
    const response = await this.client.post('/registrace', data);
    return response.data;
  }

  async login(data: LoginData): Promise<AuthResponse> {
    const response = await this.client.post('/prihlaseni', data);
    const { token, refreshToken } = response.data;

    // Zabezpečené nastavení cookies
    const cookieOptions = {
      expires: data.rememberMe ? 30 : 1, // 30 dní nebo 1 den
      secure: process.env.NODE_ENV === 'production', // HTTPS pouze v produkci
      sameSite: 'strict' as const, // CSRF ochrana
      httpOnly: false, // Musí být false pro JS přístup
    };

    Cookies.set('auth_token', token, cookieOptions);
    if (refreshToken) {
      Cookies.set('refresh_token', refreshToken, {
        ...cookieOptions,
        expires: data.rememberMe ? 90 : 7, // 90 dní nebo 7 dní
      });
    }

    return response.data;
  }

  async logout(): Promise<void> {
    // Zabezpečené odstranění cookies se stejnými parametry jako při nastavení
    const cookieOptions = {
      secure: process.env.NODE_ENV === 'production',
      sameSite: 'strict' as const,
    };

    Cookies.remove('auth_token', cookieOptions);
    Cookies.remove('refresh_token', cookieOptions);
  }

  async verifyEmail(token: string): Promise<{ message: string }> {
    const response = await this.client.post('/overeni-emailu', { token });
    return response.data;
  }

  async resendActivation(email: string): Promise<{ message: string }> {
    const response = await this.client.post('/znovu-odeslat-aktivaci', {
      email,
    });
    return response.data;
  }

  async refreshToken(): Promise<AuthResponse> {
    const refreshToken = Cookies.get('refresh_token');
    const response = await this.client.post('/autentizace/refresh', {
      refreshToken,
    });

    const { token } = response.data;

    // Zachovej původní nastavení cookies (pokud byl refresh token dlouhodobý, zachovej to)
    const refreshTokenCookie = Cookies.get('refresh_token');
    const isLongTerm =
      refreshTokenCookie && document.cookie.includes('refresh_token');

    const cookieOptions = {
      expires: isLongTerm ? 30 : 1, // Zachovej původní délku
      secure: process.env.NODE_ENV === 'production',
      sameSite: 'strict' as const,
      httpOnly: false,
    };

    Cookies.set('auth_token', token, cookieOptions);

    return response.data;
  }

  // User endpoints
  async getProfile(): Promise<User> {
    const response = await this.client.get('/uzivatele/profil');
    return response.data;
  }

  async getUserStats(): Promise<{
    itemsCount: number;
    activeBookingsCount: number;
    pendingBookingsCount: number;
    completedBookingsCount: number;
    incomingBookingsCount: number;
    totalEarnings: number;
  }> {
    const response = await this.client.get('/uzivatele/statistiky');
    return response.data;
  }

  async getUserActivities(): Promise<
    Array<{
      type: string;
      message: string;
      date: string;
      status: string;
    }>
  > {
    const response = await this.client.get('/uzivatele/aktivity');
    return response.data;
  }

  async updateProfile(data: UpdateProfileData): Promise<User> {
    const response = await this.client.put('/uzivatele/profil', data);
    return response.data;
  }

  async getUser(id: number): Promise<User> {
    const response = await this.client.get(`/uzivatele/${id}`);
    return response.data;
  }

  // Categories endpoints
  async getCategories(): Promise<Category[]> {
    const response = await this.client.get('/kategorie');
    // API Platform vrací data v Hydra formátu
    return response.data['hydra:member'] || [];
  }

  async getCategory(id: number): Promise<Category> {
    const response = await this.client.get(`/kategorie/${id}`);
    return response.data;
  }

  // Items endpoints
  async getItems(params?: {
    page?: number;
    limit?: number;
    search?: string;
    category?: number;
    minPrice?: number;
    maxPrice?: number;
    available?: boolean;
    excludeOwn?: boolean;
    sortBy?: string;
    sortOrder?: string;
  }): Promise<ApiResponse<Item[]>> {
    const response = await this.client.get('/predmety', { params });
    return response.data;
  }

  async getItem(id: number): Promise<Item> {
    const response = await this.client.get(`/predmety/${id}`);
    return response.data;
  }

  async getMyItems(): Promise<ApiResponse<Item[]>> {
    const response = await this.client.get('/uzivatele/predmety');
    return response.data;
  }

  async createItem(data: Partial<Item>): Promise<Item> {
    const response = await this.client.post('/predmety', data);
    return response.data;
  }

  async updateItem(id: number, data: Partial<Item>): Promise<Item> {
    const response = await this.client.put(`/predmety/${id}`, data);
    return response.data;
  }

  async deleteItem(id: number): Promise<void> {
    await this.client.delete(`/predmety/${id}`);
  }

  // Bookings endpoints
  async getBookings(params?: {
    status?: string;
    type?: 'outgoing' | 'incoming';
  }): Promise<Booking[]> {
    const response = await this.client.get('/pujcovani', { params });
    // API Platform vrací data v Hydra formátu
    return response.data['hydra:member'] || [];
  }

  async getBooking(id: number): Promise<Booking> {
    const response = await this.client.get(`/pujcovani/${id}`);
    return response.data;
  }

  async createBooking(data: {
    itemId: number;
    startDate: string;
    endDate: string;
    message?: string;
  }): Promise<Booking> {
    const response = await this.client.post('/pujcovani', data);
    return response.data;
  }

  async approveBooking(id: number): Promise<Booking> {
    const response = await this.client.put(`/pujcovani/${id}/schvalit`);
    return response.data;
  }

  async rejectBooking(id: number, reason?: string): Promise<Booking> {
    const response = await this.client.put(`/pujcovani/${id}/zamitnout`, {
      reason,
    });
    return response.data;
  }

  async completeBooking(id: number): Promise<Booking> {
    const response = await this.client.put(`/pujcovani/${id}/dokoncit`);
    return response.data;
  }

  async cancelBooking(id: number): Promise<Booking> {
    const response = await this.client.put(`/pujcovani/${id}/zrusit`);
    return response.data;
  }

  // Reviews endpoints
  async getItemReviews(itemId: number): Promise<{
    data: Review[];
    averageRating: number;
    totalReviews: number;
  }> {
    const response = await this.client.get(`/predmety/${itemId}/recenze`);
    return response.data;
  }

  async getUserReviews(userId: number): Promise<Review[]> {
    const response = await this.client.get(`/uzivatele/${userId}/recenze`);
    return response.data;
  }

  async createReview(
    bookingId: number,
    data: {
      rating: number;
      comment?: string;
      reviewType: 'item' | 'user';
    }
  ): Promise<Review> {
    const response = await this.client.post(
      `/pujcovani/${bookingId}/recenze`,
      data
    );
    return response.data;
  }
}

export const apiClient = new ApiClient();
