import { Item } from '@/types';

/**
 * Získá cenu předmětu z různých formátů, k<PERSON><PERSON> m<PERSON> backend posílat
 * @param item - Předmět s cenou
 * @returns Cena jako č<PERSON>lo nebo null pokud nen<PERSON> dostu<PERSON>
 */
export function getItemPrice(item: Item | any): number | null {
  // Zkus různé formáty ceny z backendu
  if (item.dailyPrice) return Number(item.dailyPrice);
  if (item.pricePerDay) return Number(item.pricePerDay);
  if (item.price?.daily) return Number(item.price.daily);
  return null;
}

/**
 * Formátuje cenu pro zobrazení
 * @param price - Cena jako <PERSON>lo
 * @param currency - Měna (výchozí: 'Kč')
 * @param period - Období (výchozí: 'den')
 * @returns Formátovaný řetězec s cenou
 */
export function formatPrice(
  price: number | null,
  currency: string = 'Kč',
  period: string = 'den'
): string {
  if (price === null || price === undefined) return 'N/A';
  return `${price} ${currency}/${period}`;
}

/**
 * Získá a zformátuje cenu předmětu
 * @param item - Předmět s cenou
 * @param currency - Měna (výchozí: 'Kč')
 * @param period - Období (výchozí: 'den')
 * @returns Formátovaný řetězec s cenou
 */
export function getFormattedItemPrice(
  item: Item | any,
  currency: string = 'Kč',
  period: string = 'den'
): string {
  const price = getItemPrice(item);
  return formatPrice(price, currency, period);
}
