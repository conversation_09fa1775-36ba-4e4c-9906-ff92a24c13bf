export interface User {
  id: number;
  email: string;
  firstName: string;
  lastName: string;
  phone?: string;
  avatar?: string;
  rating?: number;
  reviewsCount: number;
  isVerified: boolean;
  createdAt: string;
  address?: Address;
}

export interface Address {
  id: number;
  street: string;
  city: string;
  zipCode: string;
}

export interface Category {
  id: number;
  name: string;
  slug: string;
  icon?: string;
  parent?: Category;
  children?: Category[];
}

export interface ItemImage {
  id: number;
  filename: string;
  originalFilename?: string;
  fileSize?: number;
  mimeType?: string;
  isPrimary: boolean;
  sortOrder: number;
  createdAt: string;
  urls: {
    thumbnail: string;
    medium: string;
    large: string;
    original: string;
  };
}

export interface Item {
  id: number;
  title: string;
  description: string;
  category?: Category;
  hourlyPrice?: number;
  dailyPrice?: number;
  weeklyPrice?: number;
  pricePerDay?: number; // Backward compatibility
  deposit?: number;
  condition?: 'excellent' | 'good' | 'fair' | 'poor';
  conditions?: string;
  images?: string[]; // Backward compatibility
  itemImages?: ItemImage[]; // New image system
  owner?: User;
  rating?: number;
  averageRating?: number;
  reviewsCount?: number;
  isAvailable: boolean;
  minRentalHours?: number;
  maxRentalDays?: number;
  distance?: number;
  createdAt: string;
  updatedAt?: string;
}

export interface Booking {
  id: number;
  item: Item;
  borrower: User;
  startDate: string;
  endDate: string;
  status:
    | 'pending'
    | 'approved'
    | 'active'
    | 'completed'
    | 'cancelled'
    | 'rejected';
  message?: string;
  totalPrice: number;
  rejectionReason?: string;
  createdAt: string;
  updatedAt: string;
}

export interface Review {
  id: number;
  rating: number;
  comment?: string;
  reviewType: 'item' | 'user';
  author: User;
  item?: Item;
  reviewedUser?: User;
  createdAt: string;
}

export interface AuthResponse {
  token: string;
  refreshToken: string;
  user: User;
}

export interface RegisterData {
  email: string;
  password: string;
  firstName: string;
  lastName: string;
  phone?: string;
}

export interface RegisterFormData extends RegisterData {
  confirmPassword: string;
  terms: boolean;
}

export interface LoginData {
  email: string;
  password: string;
  rememberMe?: boolean;
}

export interface UpdateProfileData {
  firstName: string;
  lastName: string;
  email: string;
  phone?: string;
  address: {
    street: string;
    city: string;
    zipCode: string;
  };
}

export interface ApiResponse<T> {
  data: T;
  pagination?: {
    page: number;
    limit: number;
    total: number;
    pages: number;
    hasNext: boolean;
    hasPrev: boolean;
  };
}

export interface ApiError {
  error: string;
  message?: string;
  violations?: Array<{
    field: string;
    message: string;
  }>;
  redirectTo?: string;
  waitUntil?: string;
}
