#!/bin/bash

set -e  # Ukončit při ch<PERSON>

echo "🚀 Spouštím instalaci aplikace..."

# Inicializace Docker Swarm (pokud ještě není)
if ! docker info | grep -q "Swarm: active"; then
    echo "🔧 Inicializuji Docker Swarm..."
    docker swarm init --advertise-addr $(hostname -I | awk '{print $1}')
    echo "✅ Docker Swarm inicializován"
else
    echo "✅ Docker Swarm už je aktivní"
fi



# Základní setup
echo "⚙️ Spouštím základní setup..."
make setup

# Stažen<PERSON> externích obrazů
echo "📦 Stahuji externí obrazy..."
docker compose pull postgres redis traefik

# Build obrazů pro Swarm
echo "🔨 Sestavuji obrazy pro Docker Swarm..."
docker compose build backend frontend
echo "✅ Obrazy sestaveny: pujcovna-backend:latest, pujcovna-frontend:latest"

# Přidání hostu
echo "🌐 Přidávám nginx host..."
add-nginx-host -h pujcovna.pilarj.cz -c pujcovna_traefik -p 443 -f https

# První nasazení aplikace do Docker Swarm
echo "🚀 Nasazuji aplikaci do Docker Swarm..."
docker stack deploy -c docker-compose.swarm.yml pujcovna

echo "⏳ Čekám na spuštění služeb..."
sleep 30



echo "📊 Stav služeb:"
docker service ls
echo ""
echo "📋 Detail služeb:"
docker stack ps pujcovna

echo "✅ Instalace aplikace dokončena"
echo "🌐 Aplikace bude dostupná na: https://pujcovna.pilarj.cz"