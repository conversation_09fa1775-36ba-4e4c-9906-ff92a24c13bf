.PHONY: help build up down restart logs shell-backend shell-frontend install-backend install-frontend test-backend test-frontend clean setup config-check set-url

# Vý<PERSON>z<PERSON> cíl
help:
	@echo "🏠 Pujčovna - P2P Sharing Platform"
	@echo ""
	@echo "Dostupné př<PERSON>azy:"
	@echo ""
	@echo "Development:"
	@echo "  setup              - Kompletní setup projektu (Docker)"
	@echo "  build              - Sestaví všechny Docker kontejnery"
	@echo "  up                 - Spustí aplikaci"
	@echo "  down               - Zastaví aplikaci"
	@echo "  restart            - Restartuje aplikaci"
	@echo "  logs               - Zobrazí logy všech služeb"
	@echo ""
	@echo "Databáze:"
	@echo "  migrate            - Spustí databázové migrace"
	@echo "  migrate-diff       - Vytvoří novou migraci"
	@echo "  fixtures           - Načte testovací data"
	@echo ""
	@echo "JWT klíče:"
	@echo "  jwt-keys           - Vygeneruje JWT klíče pro autentizaci"
	@echo "  jwt-keys-server    - Vygeneruje JWT klíče pro server (bez Docker)"
	@echo ""
	@echo "Traefik:"
	@echo "  traefik-dashboard  - Otevře Traefik dashboard"
	@echo ""
	@echo "Email testování:"
	@echo "  mailhog            - Otevře MailHog web UI"
	@echo ""
	@echo "Konfigurace:"
	@echo "  config-check       - Zkontroluje konfiguraci prostředí"
	@echo "  set-url URL=...    - Nastaví URL aplikace (např. make set-url URL=http://localhost:8080)"
	@echo "  test-email         - Pošle testovací email"
	@echo ""
	@echo "Utility:"
	@echo "  shell-backend      - Otevře shell v backend kontejneru"
	@echo "  shell-frontend     - Otevře shell v frontend kontejneru"
	@echo "  test-backend       - Spustí backend testy"
	@echo "  test-frontend      - Spustí frontend testy"
	@echo "  clean              - Vyčistí Docker cache a volumes"
	@echo ""
	@echo "Testování aplikace:"
	@echo "  test-api           - Testuje API endpointy"
	@echo "  health             - Zkontroluje zdraví všech služeb"
	@echo "  test-all           - Spustí všechny testy"
	@echo "  config-check       - Zkontroluje konfiguraci prostředí"
	@echo ""
	@echo "Kontrola standardů kódu:"
	@echo "  lint               - Kontrola standardů (frontend + backend)"
	@echo "  lint-frontend      - Kontrola standardů frontendu"
	@echo "  lint-backend       - Kontrola standardů backendu"
	@echo "  fix                - Oprava standardů (frontend + backend)"
	@echo "  fix-frontend       - Oprava standardů frontendu"
	@echo "  fix-backend        - Oprava standardů backendu"

# Docker příkazy
build:
	docker compose build

up:
	docker compose up -d



down:
	docker compose down

restart: down up

logs:
	docker compose logs -f

logs-backend:
	docker compose logs -f backend

logs-frontend:
	docker compose logs -f frontend

# Shell přístup
shell-backend:
	docker compose exec backend sh

shell-frontend:
	docker compose exec frontend sh

# Instalace závislostí
install-backend:
	docker compose exec backend composer install

install-frontend:
	docker compose exec frontend npm install

# Databáze
migrate:
	docker compose exec backend php bin/console doctrine:migrations:migrate --no-interaction

migrate-diff:
	docker compose exec backend php bin/console doctrine:migrations:diff

fixtures:
	docker compose exec backend php bin/console doctrine:fixtures:load --no-interaction

# JWT klíče
jwt-keys:
	@echo "🔑 Generování JWT klíčů v Docker kontejneru..."
	docker compose exec backend mkdir -p config/jwt
	docker compose exec backend php bin/console lexik:jwt:generate-keypair --skip-if-exists
	docker compose exec backend chmod 600 config/jwt/private.pem
	docker compose exec backend chmod 644 config/jwt/public.pem
	@echo "✅ JWT klíče byly vygenerovány!"

jwt-keys-server:
	@echo "🔑 Generování JWT klíčů pro server (bez Docker)..."
	@echo "Spusť tyto příkazy na serveru v backend adresáři:"
	@echo ""
	@echo "mkdir -p config/jwt"
	@echo "php bin/console lexik:jwt:generate-keypair --skip-if-exists"
	@echo "chmod 600 config/jwt/private.pem"
	@echo "chmod 644 config/jwt/public.pem"
	@echo "php bin/console cache:clear --env=prod"
	@echo ""
	@echo "Nebo manuálně:"
	@echo "openssl genpkey -out config/jwt/private.pem -aes256 -algorithm rsa -pkcs8 -pass pass:your-jwt-passphrase"
	@echo "openssl pkey -in config/jwt/private.pem -passin pass:your-jwt-passphrase -out config/jwt/public.pem -pubout"

# Testování
test-backend:
	docker compose exec backend php bin/phpunit

test-frontend:
	docker compose exec frontend npm test

# Utility
clean:
	docker compose down -v
	docker system prune -f
	docker volume prune -f

# Kompletní setup
setup: build up install-backend install-frontend migrate fixtures
	@echo ""
	@echo "✅ Aplikace je připravena!"
	@echo ""
	@echo "🌐 Dostupné URL:"
	@echo "   Aplikace: $${APP_BASE_URL:-http://localhost:18080}"
	@echo "   Traefik Dashboard: http://localhost:18090"

# Testování API a aplikace
test-api:
	@echo "🔍 Testování API endpointů..."
	@echo "Testování kategorií..."
	@curl -s $${APP_BASE_URL:-http://localhost:18080}/api/v1/kategorie | head -n 5
	@echo "\nTestování předmětů..."
	@curl -s $${APP_BASE_URL:-http://localhost:18080}/api/v1/predmety | head -n 5
	@echo "\nTestování vyhledávání..."
	@curl -s "$${APP_BASE_URL:-http://localhost:18080}/api/v1/predmety?search=test" | head -n 5

health:
	@echo "🏥 Kontrola zdraví služeb..."
	@echo "Docker kontejnery:"
	@docker compose ps
	@echo "\nTraefik proxy:"
	@echo -n "  $${APP_BASE_URL:-http://localhost:18080}: "
	@curl -s -o /dev/null -w "%{http_code}\n" $${APP_BASE_URL:-http://localhost:18080}
	@echo "Backend API:"
	@echo -n "  $${APP_BASE_URL:-http://localhost:18080}/api/v1/health: "
	@curl -s -o /dev/null -w "%{http_code}\n" $${APP_BASE_URL:-http://localhost:18080}/api/v1/health
	@echo "Frontend přes Traefik:"
	@echo -n "  $${APP_BASE_URL:-http://localhost:18080}: "
	@curl -s -o /dev/null -w "%{http_code}\n" $${APP_BASE_URL:-http://localhost:18080}

test-all: test-api health
	@echo "✅ Všechny testy dokončeny!"

config-check:
	@echo "🔧 Kontrola konfigurace prostředí..."
	@echo "Aktuální proměnné prostředí:"
	@echo "  APP_BASE_URL: $${APP_BASE_URL:-NENÍ NASTAVENO}"
	@echo "  FRONTEND_BASE_URL: $${FRONTEND_BASE_URL:-NENÍ NASTAVENO}"
	@echo "  FRONTEND_HOST: $${FRONTEND_HOST:-NENÍ NASTAVENO}"
	@echo "  NEXT_PUBLIC_API_URL: $${NEXT_PUBLIC_API_URL:-NENÍ NASTAVENO}"
	@echo ""
	@echo "Kontrola konfigurace v kontejnerech:"
	@echo -n "Backend MailService: "
	@docker compose exec backend php bin/console debug:container App\\Service\\MailService --show-arguments 2>/dev/null | grep -E "(frontendBaseUrl|fromEmail)" || echo "OK"
	@echo -n "Frontend Next.js config: "
	@docker compose exec frontend node -e "console.log('Next.js config OK')" 2>/dev/null || echo "Kontejner není spuštěn"

# Nastavení URL aplikace
set-url:
	@if [ -z "$(URL)" ]; then \
		echo "❌ Chyba: Musíte zadat URL pomocí URL=http://example.com"; \
		echo "Příklad: make set-url URL=http://localhost:8080"; \
		exit 1; \
	fi
	@echo "🔧 Nastavuji APP_BASE_URL na: $(URL)"
	@if [ ! -f .env ]; then \
		echo "📝 Vytvářím .env soubor z .env.example..."; \
		cp .env.example .env; \
	fi
	@if grep -q "^APP_BASE_URL=" .env; then \
		sed -i.bak 's|^APP_BASE_URL=.*|APP_BASE_URL=$(URL)|' .env && rm .env.bak; \
	else \
		echo "APP_BASE_URL=$(URL)" >> .env; \
	fi
	@if grep -q "^FRONTEND_BASE_URL=" .env; then \
		sed -i.bak 's|^FRONTEND_BASE_URL=.*|FRONTEND_BASE_URL=$(URL)|' .env && rm .env.bak; \
	else \
		echo "FRONTEND_BASE_URL=$(URL)" >> .env; \
	fi
	@if grep -q "^NEXT_PUBLIC_API_URL=" .env; then \
		sed -i.bak 's|^NEXT_PUBLIC_API_URL=.*|NEXT_PUBLIC_API_URL=$(URL)/api/v1|' .env && rm .env.bak; \
	else \
		echo "NEXT_PUBLIC_API_URL=$(URL)/api/v1" >> .env; \
	fi
	@echo "✅ URL nastaveno! Restartujte kontejnery: make restart"

# Kontrola standardů kódu
lint: lint-frontend lint-backend
	@echo "✅ Kontrola standardů dokončena!"

lint-frontend:
	@echo "🔍 Kontrola standardů frontendu..."
	docker compose exec frontend npm run lint
	docker compose exec frontend npm run format:check
	docker compose exec frontend npm run type-check

lint-backend:
	@echo "🔍 Kontrola standardů backendu..."
	docker compose exec backend composer validate --strict
	docker compose exec backend composer cs-check
	docker compose exec backend composer phpstan

fix: fix-frontend fix-backend
	@echo "✅ Oprava standardů dokončena!"

fix-frontend:
	@echo "🔧 Oprava standardů frontendu..."
	docker compose exec frontend npm run lint:fix
	docker compose exec frontend npm run format

fix-backend:
	@echo "🔧 Oprava standardů backendu..."
	docker compose exec backend composer cs-fix

# Traefik příkazy
traefik-dashboard:
	@echo "🚀 Otevírám Traefik dashboard..."
	@echo "Dashboard: http://localhost:18090"
	@which open >/dev/null 2>&1 && open http://localhost:18090 || echo "Otevři ručně: http://localhost:18090"

# Email příkazy
mailhog:
	@echo "📧 Otevírám MailHog web UI..."
	@echo "MailHog: http://localhost:8025"
	@which open >/dev/null 2>&1 && open http://localhost:8025 || echo "Otevři ručně: http://localhost:8025"

test-email:
	@echo "📧 Posílám testovací email pro reset hesla..."
	@curl -X POST $${APP_BASE_URL:-http://localhost:18080}/api/v1/zapomenute-heslo \
		-H "Content-Type: application/json" \
		-d '{"email":"<EMAIL>"}' \
		&& echo "\n✅ Email odeslán! Zkontroluj MailHog na http://localhost:8025"


