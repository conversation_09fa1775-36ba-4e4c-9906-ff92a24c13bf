#!/bin/bash

set -e  # Ukončit při chybě

echo "🚀 Spouštím bezvýpadkový deployment aplikace..."

# Soubor pro správu verzí
VERSION_FILE=".deployment-versions"

# Globální proměnné pro rollback
ROLLBACK_NEEDED=false
PREVIOUS_BACKEND_IMAGE=""
PREVIOUS_FRONTEND_IMAGE=""
NEW_VERSION=""

# Funkce pro načtení aktuálních verzí ze souboru
load_current_versions() {
    if [ -f "$VERSION_FILE" ]; then
        source "$VERSION_FILE"
        echo "📋 Načteny aktuální verze ze souboru:"
        echo "  📦 Backend: $CURRENT_BACKEND_IMAGE"
        echo "  📦 Frontend: $CURRENT_FRONTEND_IMAGE"
    else
        echo "⚠️ Soubor s verzemi neexistuje - první deployment"
        CURRENT_BACKEND_IMAGE="pujcovna-backend:latest"
        CURRENT_FRONTEND_IMAGE="pujcovna-frontend:latest"
    fi

    # Export proměnných pro docker-compose
    export CURRENT_BACKEND_IMAGE
    export CURRENT_FRONTEND_IMAGE
}

# Funkce pro uložení nových verzí do souboru
save_new_versions() {
    local backend_image=$1
    local frontend_image=$2

    cat > "$VERSION_FILE" << EOF
# Aktuální verze obrazů pro deployment
CURRENT_BACKEND_IMAGE="$backend_image"
CURRENT_FRONTEND_IMAGE="$frontend_image"
PREVIOUS_BACKEND_IMAGE="$PREVIOUS_BACKEND_IMAGE"
PREVIOUS_FRONTEND_IMAGE="$PREVIOUS_FRONTEND_IMAGE"
EOF

    echo "💾 Uloženy nové verze do souboru $VERSION_FILE"
}

# Funkce pro rollback
rollback_deployment() {
    echo "🔄 Spouštím rollback na předchozí verze..."

    if [ -n "$PREVIOUS_BACKEND_IMAGE" ] && [ -n "$PREVIOUS_FRONTEND_IMAGE" ]; then
        echo "  📦 Vracím předchozí obrazy: $PREVIOUS_BACKEND_IMAGE, $PREVIOUS_FRONTEND_IMAGE"

        # Export proměnných pro docker-compose
        export CURRENT_BACKEND_IMAGE="$PREVIOUS_BACKEND_IMAGE"
        export CURRENT_FRONTEND_IMAGE="$PREVIOUS_FRONTEND_IMAGE"

        # Deploy předchozích verzí
        echo "  🚀 Nasazuji předchozí verze služeb..."
        docker stack deploy -c docker-compose.swarm.yml pujcovna

        # Čekání na rollback backendu
        echo "  📡 Čekám na rollback backendu..."
        if wait_for_rollout "backend"; then
            echo "  ✅ Rollback backendu dokončen"
        else
            echo "  ❌ Rollback backendu selhal"
        fi

        # Čekání na rollback frontendu
        echo "  🌐 Čekám na rollback frontendu..."
        if wait_for_rollout "frontend"; then
            echo "  ✅ Rollback frontendu dokončen"
        else
            echo "  ❌ Rollback frontendu selhal"
        fi

        echo "✅ Rollback dokončen - aplikace vrácena na předchozí funkční verzi"

        # Uložení rollback verzí do souboru
        save_new_versions "$PREVIOUS_BACKEND_IMAGE" "$PREVIOUS_FRONTEND_IMAGE"
    else
        echo "❌ Nelze provést rollback - předchozí verze nejsou k dispozici"
    fi
}

# Trap pro zachycení chyb a spuštění rollback
trap 'if [ "$ROLLBACK_NEEDED" = true ]; then rollback_deployment; fi' EXIT

# Funkce pro test HTTP endpointu
test_endpoint() {
    local url=$1
    local max_attempts=20
    local attempt=1

    echo "  🌐 Testuji endpoint $url..."

    while [ $attempt -le $max_attempts ]; do
        if curl -s -f "$url" > /dev/null 2>&1; then
            echo "  ✅ Endpoint $url odpovídá (pokus $attempt/$max_attempts)"
            return 0
        fi

        echo "  ⏳ Čekám na endpoint $url (pokus $attempt/$max_attempts)..."
        sleep 3
        attempt=$((attempt + 1))
    done

    echo "  ❌ Endpoint $url neodpovídá po $max_attempts pokusech"
    return 1
}

# Funkce pro čekání na dokončení rolling update v Docker Swarm
wait_for_rollout() {
    local service=$1
    local max_attempts=60
    local attempt=1

    echo "  ⏳ Čekám na dokončení rolling update služby $service..."

    while [ $attempt -le $max_attempts ]; do
        local service_status=$(docker service ps pujcovna_$service --format "{{.CurrentState}}" --no-trunc | head -2)
        local running_count=$(echo "$service_status" | grep -c "Running" 2>/dev/null || echo "0")
        local expected_replicas=2

        # Zajistíme, že running_count je číslo
        if ! [[ "$running_count" =~ ^[0-9]+$ ]]; then
            running_count=0
        fi

        if [ "$running_count" -eq "$expected_replicas" ]; then
            echo "  ✅ Rolling update služby $service dokončen (pokus $attempt/$max_attempts)"
            return 0
        fi

        echo "  ⏳ Rolling update služby $service probíhá... ($running_count/$expected_replicas replik) (pokus $attempt/$max_attempts)"
        sleep 5
        attempt=$((attempt + 1))
    done

    echo "  ❌ Rolling update služby $service se nedokončil po $max_attempts pokusech"
    return 1
}

# Načtení aktuálních verzí
load_current_versions

# Nastavení předchozích verzí pro rollback
PREVIOUS_BACKEND_IMAGE="$CURRENT_BACKEND_IMAGE"
PREVIOUS_FRONTEND_IMAGE="$CURRENT_FRONTEND_IMAGE"

# Stažení nejnovějších externích obrazů
echo "📦 Stahuji nejnovější externí obrazy..."
docker compose -f docker-compose.yml pull redis traefik

# Spuštění lokálního registru, pokud neběží
echo "🔄 Kontroluji lokální Docker registry..."
if ! docker ps | grep -q "registry:2"; then
  echo "🚀 Spouštím lokální Docker registry..."
  docker run -d -p 5000:5000 --restart=always --name registry registry:2
else
  echo "✅ Lokální Docker registry již běží"
fi

# Build nových obrazů pro Swarm s verzí
echo "🔨 Sestavuji nové obrazy..."
NEW_VERSION=$(date +%Y%m%d%H%M%S)
docker compose -f docker-compose.yml build --no-cache backend frontend

# Označení a push do lokálního registru
echo "📤 Nahrávám obrazy do lokálního registru..."
NEW_BACKEND_IMAGE="localhost:5000/pujcovna-backend:$NEW_VERSION"
NEW_FRONTEND_IMAGE="localhost:5000/pujcovna-frontend:$NEW_VERSION"

docker tag pujcovna-backend:latest $NEW_BACKEND_IMAGE
docker tag pujcovna-frontend:latest $NEW_FRONTEND_IMAGE
docker push $NEW_BACKEND_IMAGE
docker push $NEW_FRONTEND_IMAGE
echo "✅ Obrazy nahrány: $NEW_BACKEND_IMAGE, $NEW_FRONTEND_IMAGE"

# Ověření, že Docker Swarm je aktivní
if ! docker info | grep -q "Swarm: active"; then
    echo "❌ Docker Swarm není aktivní. Spusť nejdříve install.sh"
    exit 1
fi

# Bezvýpadkový rolling update pomocí Docker Swarm
echo "🔄 Spouštím bezvýpadkový rolling update pomocí Docker Swarm..."

# Nastavení rollback flagu - od tohoto bodu může být potřeba rollback
ROLLBACK_NEEDED=true

# Export nových verzí pro docker-compose
export CURRENT_BACKEND_IMAGE="$NEW_BACKEND_IMAGE"
export CURRENT_FRONTEND_IMAGE="$NEW_FRONTEND_IMAGE"

# Deploy celého stacku - Docker Swarm automaticky provede rolling update
echo "  🚀 Nasazuji nové verze služeb..."
docker stack deploy -c docker-compose.swarm.yml pujcovna

# Čekání na dokončení rolling update backendu
echo "  📡 Čekám na rolling update backendu..."
if ! wait_for_rollout "backend"; then
    echo "❌ Rolling update backendu selhal"
    rollback_deployment
    exit 1
fi

# Test backend API
if ! test_endpoint "https://pujcovna.pilarj.cz/api/v1/health"; then
    echo "❌ Backend API neodpovídá po update"
    rollback_deployment
    exit 1
fi

# 2. Spuštění databázových migrací
echo "  🗄️ Spouštím databázové migrace..."
# Najdeme jeden z backend kontejnerů pro spuštění migrací
BACKEND_CONTAINER=$(docker ps --filter "name=pujcovna_backend" --format "{{.Names}}" | head -1)
if [ -z "$BACKEND_CONTAINER" ]; then
    echo "❌ Nenašel jsem backend kontejner"
    rollback_deployment
    exit 1
fi

if ! docker exec $BACKEND_CONTAINER php bin/console doctrine:migrations:migrate --no-interaction; then
    echo "❌ Migrace selhaly"
    rollback_deployment
    exit 1
fi

# Čekání na dokončení rolling update frontendu
echo "  🌐 Čekám na rolling update frontendu..."
if ! wait_for_rollout "frontend"; then
    echo "❌ Rolling update frontendu selhal"
    rollback_deployment
    exit 1
fi



# Test frontend
if ! test_endpoint "https://pujcovna.pilarj.cz"; then
    echo "❌ Frontend neodpovídá po update"
    rollback_deployment
    exit 1
fi

# Finální test celé aplikace
echo "🧪 Finální test aplikace..."
if ! test_endpoint "https://pujcovna.pilarj.cz" || ! test_endpoint "https://pujcovna.pilarj.cz/api/v1/health"; then
    echo "❌ Aplikace neodpovídá správně po deploymetu"
    rollback_deployment
    exit 1
fi

# Deployment byl úspěšný - vypneme rollback
ROLLBACK_NEEDED=false

# Uložení nových verzí jako aktuálních
save_new_versions "$NEW_BACKEND_IMAGE" "$NEW_FRONTEND_IMAGE"

# Vyčištění starých neúspěšných úloh
echo "🧹 Čistím staré neúspěšné úlohy..."
docker service update --force pujcovna_backend > /dev/null 2>&1 || echo "Backend už je aktuální"
docker service update --force pujcovna_frontend > /dev/null 2>&1 || echo "Frontend už je aktuální"

# Vyčištění starých obrazů z lokálního registru
echo "🧹 Čistím staré obrazy z registru..."

# Získání seznamu všech tagů pro backend a frontend
BACKEND_TAGS=$(curl -s http://localhost:5000/v2/pujcovna-backend/tags/list | grep -o '"[0-9]*"' | tr -d '"' | sort -r)
FRONTEND_TAGS=$(curl -s http://localhost:5000/v2/pujcovna-frontend/tags/list | grep -o '"[0-9]*"' | tr -d '"' | sort -r)

# Ponechání pouze 5 nejnovějších verzí pro backend
if [ -n "$BACKEND_TAGS" ]; then
    BACKEND_TO_DELETE=$(echo "$BACKEND_TAGS" | tail -n +6)
    for tag in $BACKEND_TO_DELETE; do
        echo "  🗑️ Mažu starý backend tag: $tag"
        DIGEST=$(curl -s -H "Accept: application/vnd.docker.distribution.manifest.v2+json" \
                 http://localhost:5000/v2/pujcovna-backend/manifests/$tag | \
                 grep -o '"digest":"[^"]*"' | cut -d'"' -f4)
        if [ -n "$DIGEST" ]; then
            curl -s -X DELETE http://localhost:5000/v2/pujcovna-backend/manifests/$DIGEST > /dev/null 2>&1
        fi
    done
fi

# Ponechání pouze 5 nejnovějších verzí pro frontend
if [ -n "$FRONTEND_TAGS" ]; then
    FRONTEND_TO_DELETE=$(echo "$FRONTEND_TAGS" | tail -n +6)
    for tag in $FRONTEND_TO_DELETE; do
        echo "  🗑️ Mažu starý frontend tag: $tag"
        DIGEST=$(curl -s -H "Accept: application/vnd.docker.distribution.manifest.v2+json" \
                 http://localhost:5000/v2/pujcovna-frontend/manifests/$tag | \
                 grep -o '"digest":"[^"]*"' | cut -d'"' -f4)
        if [ -n "$DIGEST" ]; then
            curl -s -X DELETE http://localhost:5000/v2/pujcovna-frontend/manifests/$DIGEST > /dev/null 2>&1
        fi
    done
fi

# Spuštění garbage collection v registru
echo "🧹 Spouštím garbage collection v registru..."
docker exec registry bin/registry garbage-collect /etc/docker/registry/config.yml > /dev/null 2>&1 || echo "  ⚠️ Garbage collection selhal"

# Vyčištění lokálních obrazů
echo "🧹 Čistím lokální obrazy..."
docker image prune -f

echo "✅ Bezvýpadkový deployment dokončen úspěšně!"
echo "🌐 Aplikace je dostupná na: https://pujcovna.pilarj.cz"
echo "📊 Stav služeb:"
docker service ls
echo ""
echo "📋 Detail služeb:"
docker stack ps pujcovna
