# Sentry Integrace

Aplikace má integrované logování chyb do Sentry pro backend i frontend.

## Konfigurace

### Backend (Symfony)
V `.env` souboru nastavte:
```
# Sentry - Backend
SENTRY_DSN=https://<EMAIL>/4509559654580225
SENTRY_ENVIRONMENT=development
SENTRY_TRACES_SAMPLE_RATE=1.0
```

### Frontend (Next.js)
V `.env` souboru nastavte:
```
# Sentry - Frontend
NEXT_PUBLIC_SENTRY_DSN=https://<EMAIL>/4509559659102208
NEXT_PUBLIC_SENTRY_ENVIRONMENT=development
NEXT_PUBLIC_SENTRY_TRACES_SAMPLE_RATE=1.0
```

## Použití

### Backend

#### Automatické logování
Všechny neošetřené chyby se automaticky logují do Sentry včetně:
- Kontextu o requestu (URL, metoda, IP, user agent)
- Informací o přihlášeném uživateli
- Route a controller informace

#### Manuální logování
```php
use App\Service\SentryService;

class SomeController extends AbstractController
{
    public function __construct(
        private SentryService $sentryService
    ) {}

    public function someAction(): Response
    {
        try {
            // nějaký kód
        } catch (\Exception $e) {
            $this->sentryService->logError($e, [
                'additional_context' => 'some value'
            ]);
        }

        // Logování zprávy
        $this->sentryService->logMessage(
            'Něco se stalo',
            'warning',
            ['extra_data' => 'value']
        );

        // Nastavení uživatelského kontextu
        $this->sentryService->setUserContext([
            'id' => $user->getId(),
            'email' => $user->getEmail(),
            'username' => $user->getUserIdentifier()
        ]);
    }
}
```

### Frontend

#### Automatické logování
- Všechny neošetřené chyby se automaticky logují při inicializaci
- ErrorBoundary komponenta zachytává React chyby

#### Manuální logování
```typescript
import { logError, logMessage, setUserContext, addBreadcrumb } from '@/lib/sentry';

// Logování chyby
try {
  // nějaký kód
} catch (error) {
  logError(error, { context: 'additional info' });
}

// Logování zprávy
logMessage('Něco se stalo', 'info', { extra: 'data' });

// Nastavení uživatelského kontextu
setUserContext({
  id: user.id,
  email: user.email,
  username: user.username
});

// Přidání breadcrumb
addBreadcrumb('Uživatel klikl na tlačítko', 'user', { button: 'submit' });
```

#### ErrorBoundary komponenta
```tsx
import ErrorBoundary from '@/components/ErrorBoundary';

<ErrorBoundary>
  <YourComponent />
</ErrorBoundary>
```



## Konfigurace prostředí

### Development
```
SENTRY_ENVIRONMENT=development
SENTRY_TRACES_SAMPLE_RATE=1.0
NEXT_PUBLIC_SENTRY_ENVIRONMENT=development
NEXT_PUBLIC_SENTRY_TRACES_SAMPLE_RATE=1.0
```

### Production
```
SENTRY_ENVIRONMENT=production
SENTRY_TRACES_SAMPLE_RATE=0.1
NEXT_PUBLIC_SENTRY_ENVIRONMENT=production
NEXT_PUBLIC_SENTRY_TRACES_SAMPLE_RATE=0.1
```

### Parametry konfigurace

#### Backend
- `SENTRY_DSN` - DSN pro backend projekt
- `SENTRY_ENVIRONMENT` - prostředí (development/production/staging)
- `SENTRY_TRACES_SAMPLE_RATE` - procento trasování (0.0-1.0)

#### Frontend
- `NEXT_PUBLIC_SENTRY_DSN` - DSN pro frontend projekt
- `NEXT_PUBLIC_SENTRY_ENVIRONMENT` - prostředí (development/production/staging)
- `NEXT_PUBLIC_SENTRY_TRACES_SAMPLE_RATE` - procento trasování (0.0-1.0)

## Ignorované chyby

Backend automaticky ignoruje:
- HTTP 4xx chyby (kromě 500+)
- Symfony FatalError exceptions
- NotFoundHttpException
- AccessDeniedException

## Monitoring

Sentry dashboard: https://sentry.io/organizations/pilarj/projects/

### Backend projekt
- DSN: `https://<EMAIL>/4509559654580225`
- Projekt: pujcovna-backend

### Frontend projekt  
- DSN: `https://<EMAIL>/4509559659102208`
- Projekt: pujcovna-frontend
